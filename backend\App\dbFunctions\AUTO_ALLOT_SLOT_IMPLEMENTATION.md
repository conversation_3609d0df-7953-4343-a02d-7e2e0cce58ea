# Auto-Allot Slot Implementation

## Overview
This document describes the implementation of the auto-allot slot logic for the TMS system. The implementation follows the user's requirements for automatic booking slot assignment when a service request is created with a capacity_id.

## Implementation Details

### 1. Modified `tms_create_service_request` Function
**File**: `backend\App\dbFunctions\tms_create_service_request.sql`

**Changes Made**:
- Added `booking_id_` variable to handle booking IDs
- Added extraction of `booking_id` from form_data
- Added auto-allot slot logic after service provider assignment
- Added booking_id handling in the UPDATE section
- Added timeline entry for successful booking slot assignments

**Key Addition**:
```sql
-- Auto-allot slot logic: if form_data has capacity_id, call tms_create_booking
if capacity_id_ is not null then
    PERFORM tms_create_booking(ins_id, form_data_);
end if;
```

### 2. Created `tms_create_booking` Function
**File**: `backend\App\dbFunctions\tms_create_booking.sql`

**Purpose**: Handles the core booking logic as specified by the user requirements.

**Parameters**:
- `ins_id_` (bigint): The service request ID
- `form_data_` (json): Contains booking parameters and user context

**Booking Logic Implementation**:

#### Step 1: Parameter Extraction
- Extracts manpower, job_duration, hub_travel_time from form_data
- Calculates total_duration = job_duration + hub_travel_time
- Calculates man_minutes = total_duration * manpower

#### Step 2: Capacity Validation
- Gets available_capacity, total_cap_in_minutes, booked_cap_in_minutes from cl_tx_capacity table
- Calculates remaining_capacity = total_cap_in_minutes - booked_cap_in_minutes

#### Step 3: Booking Check
- Checks if ins_id already exists in cl_tx_bookings table (order_id column)
- Determines if this is a new booking or an update

#### Step 4: Booking Logic (Book capacity within the slot)
- **Condition**: If Manpower >= available_capacity
- **Capacity Check**: If man_minutes <= remaining_capacity
  - **Yes**: Proceed with booking
    - Create/Update booking in cl_tx_bookings table
    - Update capacity table's booked_cap_in_minutes
    - Create booking_details JSON
    - Call tms_create_service_request again to update booking columns
    - Add timeline entry
  - **No**: Return "Booking unavailable - insufficient capacity"

#### Step 5: Service Request Update
- Creates user context JSON
- Builds booking update JSON with booking_id, capacity_id, and booking_details
- Calls `tms_create_service_request` again to update the service request with booking information

## Database Tables Involved

### 1. cl_tx_capacity
- Stores capacity information for resources
- Columns: available_capacity, total_cap_in_minutes, booked_cap_in_minutes

### 2. cl_tx_bookings
- Stores individual booking records
- Columns: capacity_id, order_id, booked_qty
- Foreign keys to cl_tx_capacity and cl_tx_srvc_req

### 3. cl_tx_srvc_req
- Service request table
- Booking-related columns: capacity_id, booking_id, booking_details

## Flow Diagram

```
Service Request Creation with capacity_id
    ↓
tms_create_service_request called
    ↓
Auto-allot logic triggered (capacity_id exists)
    ↓
tms_create_booking called
    ↓
Check if booking exists (order_id in cl_tx_bookings)
    ↓
Calculate booking requirements (man_minutes)
    ↓
Check capacity availability
    ↓
If sufficient capacity:
    - Create/Update booking record
    - Update capacity table
    - Call tms_create_service_request again
    - Add timeline entry
    ↓
If insufficient capacity:
    - Return booking unavailable message
```

## Error Handling

The implementation includes comprehensive error handling:
- Validates required parameters (ins_id, capacity_id)
- Checks if capacity exists
- Handles both new bookings and updates
- Provides detailed error messages for insufficient capacity or manpower
- Properly updates capacity calculations for booking updates

## Timeline Integration

The system automatically adds timeline entries for:
- Successful booking slot assignments
- Booking cancellations (existing functionality)

## Future Enhancements

The current implementation handles "Book capacity within the slot" scenario. Future enhancements could include:
- "Book capacity across slots" for durations greater than slot duration
- More sophisticated capacity allocation algorithms
- Integration with hub travel time calculations
- Advanced booking conflict resolution
