import RemoteSourceSelect from '../../../../components/wify-utils/RemoteSourceSelect';

const srvcHubFormMeta = (
    form,
    editMode,
    state_list,
    hubFormConfig,
    setHubFormConfig
) => {
    return {
        columns: 1,
        formItemLayout: null,
        fields: [
            {
                key: 'hub_name',
                label: 'Hub Name',
                placeholder: 'Hub Name',
                widget: 'input',
                quick: true,
                required: true,
                rules: [
                    {
                        max: 100,
                        message: 'Hub name cannot exceed 100 characters',
                    },
                ],
            },
            {
                key: 'hub_code',
                label: 'Hub Code',
                placeholder: 'Hub Code',
                widget: 'input',
                quick: true,
                required: true,
                rules: [
                    {
                        max: 20,
                        message: 'Hub code cannot exceed 20 characters',
                    },
                ],
            },
            {
                key: 'state',
                label: 'State',
                placeholder: 'Select State',
                widget: 'select',
                quick: true,
                required: true,
                widgetProps: {
                    optionFilterProp: 'children',
                    showSearch: true,
                },
                options: state_list,
                onChange: (value) => {
                    setHubFormConfig((prevConfig) => ({
                        ...prevConfig,
                        selectedState: value,
                        selectedCity: null,
                        selectedPincodes: [],
                    }));
                    form.setFieldsValue({
                        city: undefined,
                        pin_codes: [],
                    });
                },
            },
            {
                key: 'city',
                label: 'City',
                colSpan: 2,
                widget: RemoteSourceSelect,
                quick: true,
                required: true,
                widgetProps: {
                    key: hubFormConfig.selectedState,
                    mode: 'single',
                    url: '/searcher',
                    placeholder: 'Type City',
                    params: {
                        fn: 'getCitiesByState',
                        state: hubFormConfig.selectedState,
                    },
                    widgetProps: {
                        mode: 'single',
                        labelInValue: false,
                        showSearch: true,
                        style: { width: '100%' },
                    },
                },
                onChange: (value) => {
                    // setSelectedCity(value);
                    setHubFormConfig((prevConfig) => ({
                        ...prevConfig,
                        selectedCity: value,
                        selectedPincodes: [],
                    }));
                    form.setFieldsValue({
                        pin_codes: [],
                    });
                },
            },
            {
                key: 'pin_codes',
                label: 'Pincodes',
                placeholder: 'Search and select pincodes',
                widget: RemoteSourceSelect,
                quick: true,
                required: true,
                rules: [
                    {
                        validator: (_, value) => {
                            if (!value) {
                                return Promise.resolve();
                            }

                            let pincodes = [];

                            // Handle different value types
                            if (Array.isArray(value)) {
                                // For dropdown selection (array of values)
                                pincodes = value;
                            } else if (typeof value === 'string') {
                                // Check for trailing comma first
                                if (/,\s*$/.test(value)) {
                                    return Promise.reject(
                                        new Error(
                                            'Please remove the extra comma at the end of the pincode list.'
                                        )
                                    );
                                }

                                // Check for other invalid patterns:
                                // - consecutive commas: "400031, ,400032" or "400031,,400032"
                                if (/,\s*,|^\s*,|^,\s*$/.test(value)) {
                                    return Promise.reject(
                                        new Error(
                                            'Please provide valid pincodes separated by commas without empty segments.'
                                        )
                                    );
                                }

                                // For manual input or bulk creation (comma-separated string)
                                pincodes = value
                                    .split(',')
                                    .map((code) => code.trim())
                                    .filter((code) => code !== '');
                            }

                            if (pincodes.length === 0) {
                                return Promise.resolve();
                            }

                            // Check for empty pincodes after processing
                            const hasEmptyPincodes = pincodes.some(
                                (code) => !code || code.trim() === ''
                            );
                            if (hasEmptyPincodes) {
                                return Promise.reject(
                                    new Error(
                                        'Pincodes cannot contain empty values. Please provide valid pincodes.'
                                    )
                                );
                            }

                            // Validate each pincode format (6 digits only)
                            const trimmedPincodes = pincodes.map((code) =>
                                code.trim()
                            );
                            const invalidNumberPincodes =
                                trimmedPincodes.filter(
                                    (pincode) => !/^\d+$/.test(pincode)
                                );
                            const invalidLengthPincodes =
                                trimmedPincodes.filter(
                                    (pincode) => pincode.length !== 6
                                );

                            // Show all invalid number pincodes
                            if (invalidNumberPincodes.length > 0) {
                                return Promise.reject(
                                    new Error(
                                        `Invalid Pincode(s): ${invalidNumberPincodes.join(', ')}. Must contain only numbers.`
                                    )
                                );
                            }
                            // Show all invalid length pincodes
                            if (invalidLengthPincodes.length > 0) {
                                return Promise.reject(
                                    new Error(
                                        `Invalid Pincode(s): ${invalidLengthPincodes.join(', ')}. Must be exactly 6 digits long.`
                                    )
                                );
                            }

                            // Check for duplicates (case-insensitive)
                            const normalizedPincodes = pincodes.map((code) =>
                                code.toLowerCase()
                            );
                            const uniquePincodes = [
                                ...new Set(normalizedPincodes),
                            ];

                            if (
                                uniquePincodes.length !==
                                normalizedPincodes.length
                            ) {
                                return Promise.reject(
                                    new Error(
                                        'Duplicate pincodes are not allowed. Please remove duplicate entries.'
                                    )
                                );
                            }

                            return Promise.resolve();
                        },
                    },
                ],
                widgetProps: {
                    key: `${hubFormConfig.selectedState}-${hubFormConfig.selectedCity}`,
                    mode: 'multiple',
                    labelInValue: false,
                    showSearch: true,
                    placeholder: 'Type to Search & Select Pincodes',
                    url: '/searcher',
                    params: {
                        fn: 'getPincodesByCity',
                        state: hubFormConfig.selectedState,
                        city: hubFormConfig.selectedCity,
                    },
                    style: { width: '100%' },
                },
                onChange: (value) => {
                    setHubFormConfig((prevConfig) => ({
                        ...prevConfig,
                        selectedPincodes: value,
                    }));
                },
            },
            {
                key: 'is_active',
                label: 'Is Active',
                widget: editMode ? 'switch' : 'input',
                widgetProps: editMode
                    ? { defaultChecked: true }
                    : { type: 'hidden' },
                initialValue: true,
                preserve: true,
                // When not in edit mode, hide the entire form item (including the label)
                formItemProps: editMode ? {} : { style: { display: 'none' } },
            },
        ],
    };
};

export default srvcHubFormMeta;
