CREATE OR REPLACE FUNCTION public.tms_create_booking(ins_id_ bigint, form_data_ json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
-- Declarations
declare
    -- Bare minimums
    status boolean := false;
    message text := 'Internal_error';
    affected_rows integer;
    resp_data json;
    
    -- Variables from form_data
    org_id_ integer;
    usr_id_ uuid;
    ip_address_ text;
    user_agent_ text;
    capacity_id_ bigint;
    
    -- Booking logic variables
    manpower_ integer;
    job_duration_ integer; -- in minutes
    hub_travel_time_ integer := 0; -- in minutes
    total_duration_ integer;
    man_minutes_ integer;
    slot_duration_ integer := 60; -- default 60 minutes per slot
    
    -- Capacity variables
    available_capacity_ integer;
    total_cap_in_minutes_ integer;
    booked_cap_in_minutes_ integer;
    remaining_capacity_ integer;
    
    -- Booking table variables
    existing_booking_id_ bigint;
    existing_booking_qty_ integer;
    booking_qty_ integer;
    
    -- Timeline and update variables
    user_context_json json;
    booking_update_json json;
    timeline_resp json;
    
begin
    -- Initialize status and message
    status := false;
    message := 'Internal_error';
    
    -- Extract data from form_data
    org_id_ := (form_data_->>'org_id')::integer;
    usr_id_ := (form_data_->>'usr_id')::uuid;
    ip_address_ := form_data_->>'ip_address';
    user_agent_ := form_data_->>'user_agent';
    capacity_id_ := (form_data_->>'capacity_id')::bigint;
    
    -- Validate required parameters
    if ins_id_ is null or capacity_id_ is null then
        return json_build_object('status', false, 'message', 'ins_id and capacity_id are required');
    end if;
    
    -- Get booking parameters from form_data
    manpower_ := COALESCE((form_data_->>'manpower')::integer, 1);
    job_duration_ := COALESCE((form_data_->>'job_duration')::integer, 60); -- default 60 minutes
    hub_travel_time_ := COALESCE((form_data_->>'hub_travel_time')::integer, 0);
    
    -- Calculate total duration and man-minutes
    total_duration_ := job_duration_ + hub_travel_time_;
    man_minutes_ := total_duration_ * manpower_;
    
    -- Get available capacity from cl_tx_capacity table
    select available_capacity, total_cap_in_minutes, booked_cap_in_minutes
      from cl_tx_capacity
     where db_id = capacity_id_
      into available_capacity_, total_cap_in_minutes_, booked_cap_in_minutes_;
    
    if available_capacity_ is null then
        return json_build_object('status', false, 'message', 'Capacity not found');
    end if;
    
    -- Calculate remaining capacity in minutes
    remaining_capacity_ := total_cap_in_minutes_ - booked_cap_in_minutes_;
    
    -- Check if ins_id is already present in booking table (order_id column)
    select db_id, booked_qty
      from cl_tx_bookings
     where order_id = ins_id_
       and capacity_id = capacity_id_
      into existing_booking_id_, existing_booking_qty_;
    
    -- Booking logic: Book capacity within the slot
    if manpower_ >= available_capacity_ then
        -- Check if man-minutes (total capacity man minutes) are available
        if man_minutes_ <= remaining_capacity_ then
            -- Proceed with booking
            booking_qty_ := man_minutes_;
            
            if existing_booking_id_ is not null then
                -- Update existing booking
                update cl_tx_bookings
                   set booked_qty = booking_qty_,
                       u_meta = row(ip_address_, user_agent_, now() at time zone 'utc')
                 where db_id = existing_booking_id_;
                
                get diagnostics affected_rows = ROW_COUNT;
                
                if affected_rows = 1 then
                    message := 'Booking updated successfully';
                else
                    message := 'Failed to update booking';
                    return json_build_object('status', false, 'message', message);
                end if;
            else
                -- Create new booking
                insert into cl_tx_bookings (
                    capacity_id, order_id, order_label, booked_qty, c_meta, u_meta
                ) values (
                    capacity_id_, ins_id_, 'Service Request', booking_qty_,
                    row(ip_address_, user_agent_, now() at time zone 'utc'),
                    row(ip_address_, user_agent_, now() at time zone 'utc')
                ) returning db_id into existing_booking_id_;
                
                get diagnostics affected_rows = ROW_COUNT;
                
                if affected_rows = 1 then
                    message := 'New booking created successfully';
                else
                    message := 'Failed to create booking';
                    return json_build_object('status', false, 'message', message);
                end if;
            end if;
            
            -- Update capacity table with new booked_cap_in_minutes
            if existing_booking_id_ is not null then
                -- Update existing booking: adjust capacity by the difference
                update cl_tx_capacity
                   set booked_cap_in_minutes = booked_cap_in_minutes - existing_booking_qty_ + booking_qty_,
                       u_meta = row(ip_address_, user_agent_, now() at time zone 'utc')
                 where db_id = capacity_id_;
            else
                -- New booking: add the booking quantity
                update cl_tx_capacity
                   set booked_cap_in_minutes = booked_cap_in_minutes + booking_qty_,
                       u_meta = row(ip_address_, user_agent_, now() at time zone 'utc')
                 where db_id = capacity_id_;
            end if;
            
            -- Prepare booking details JSON
            resp_data := json_build_object(
                'booking_id', existing_booking_id_,
                'capacity_id', capacity_id_,
                'booking_details', json_build_object(
                    'manpower', manpower_,
                    'job_duration', job_duration_,
                    'hub_travel_time', hub_travel_time_,
                    'total_duration', total_duration_,
                    'man_minutes', man_minutes_,
                    'booking_qty', booking_qty_
                )
            );
            
            -- Get user context for updating service request
            user_context_json := json_build_object(
                'org_id', org_id_,
                'usr_id', usr_id_,
                'ip_address', ip_address_,
                'user_agent', user_agent_
            );
            
            -- Create booking update JSON with booking details
            booking_update_json := jsonb_set(user_context_json::jsonb, '{booking_id}', to_jsonb(existing_booking_id_), true);
            booking_update_json := jsonb_set(booking_update_json::jsonb, '{capacity_id}', to_jsonb(capacity_id_), true);
            booking_update_json := jsonb_set(booking_update_json::jsonb, '{booking_details}', resp_data->'booking_details', true);
            
            -- Call tms_create_service_request again to update booking_ids, booking_details, capacity_id columns
            PERFORM tms_create_service_request(booking_update_json, ins_id_);
            
            status := true;
            
        else
            -- Not enough capacity available
            status := false;
            message := 'Booking unavailable - insufficient capacity';
            resp_data := json_build_object(
                'required_man_minutes', man_minutes_,
                'available_capacity_minutes', remaining_capacity_
            );
        end if;
    else
        -- Not enough manpower available
        status := false;
        message := 'Booking unavailable - insufficient manpower';
        resp_data := json_build_object(
            'required_manpower', manpower_,
            'available_manpower', available_capacity_
        );
    end if;
    
    return json_build_object('status', status, 'message', message, 'data', resp_data);
    
END;
$function$
;
