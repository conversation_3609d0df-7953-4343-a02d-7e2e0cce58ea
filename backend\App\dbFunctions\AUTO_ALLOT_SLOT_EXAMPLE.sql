-- Example usage of the Auto-Allot Slot functionality
-- This file demonstrates how the auto-allot slot logic works

-- Example 1: Creating a service request with capacity_id (triggers auto-allot)
-- This would be called from the frontend/API when creating a service request

/*
Example form_data JSON for service request creation:
{
    "org_id": 1,
    "usr_id": "123e4567-e89b-12d3-a456-426614174000",
    "ip_address": "***********",
    "user_agent": "Mozilla/5.0...",
    "srvc_type_id": 1,
    "capacity_id": 100,
    "manpower": 2,
    "job_duration": 120,
    "hub_travel_time": 30,
    "cust_full_name": "<PERSON>",
    "cust_mobile": "1234567890",
    "request_priority": "high"
}
*/

-- When tms_create_service_request is called with the above data:
-- 1. Service request is created normally
-- 2. Auto-allot logic is triggered because capacity_id exists
-- 3. tms_create_booking is called automatically
-- 4. Booking logic executes:
--    - Manpower: 2
--    - Job Duration: 120 minutes
--    - Hub Travel Time: 30 minutes
--    - Total Duration: 150 minutes
--    - Man Minutes: 2 * 150 = 300 minutes
-- 5. System checks capacity table for capacity_id = 100
-- 6. If sufficient capacity exists, booking is created
-- 7. Service request is updated with booking details
-- 8. Timeline entry is added

-- Example 2: Manual booking creation (for testing)
-- This demonstrates calling tms_create_booking directly

SELECT tms_create_booking(
    12345, -- ins_id (service request ID)
    '{
        "org_id": 1,
        "usr_id": "123e4567-e89b-12d3-a456-426614174000",
        "ip_address": "***********",
        "user_agent": "Test Agent",
        "capacity_id": 100,
        "manpower": 1,
        "job_duration": 60,
        "hub_travel_time": 15
    }'::json
);

-- Expected response for successful booking:
/*
{
    "status": true,
    "message": "New booking created successfully",
    "data": {
        "booking_id": 1,
        "capacity_id": 100,
        "booking_details": {
            "manpower": 1,
            "job_duration": 60,
            "hub_travel_time": 15,
            "total_duration": 75,
            "man_minutes": 75,
            "booking_qty": 75
        }
    }
}
*/

-- Expected response for insufficient capacity:
/*
{
    "status": false,
    "message": "Booking unavailable - insufficient capacity",
    "data": {
        "required_man_minutes": 300,
        "available_capacity_minutes": 200
    }
}
*/

-- Example 3: Checking booking status
-- Query to see if a service request has booking information

SELECT 
    sr.db_id as service_request_id,
    sr.display_code,
    sr.capacity_id,
    sr.booking_id,
    sr.booking_details,
    b.booked_qty,
    c.available_capacity,
    c.booked_cap_in_minutes,
    c.total_cap_in_minutes
FROM cl_tx_srvc_req sr
LEFT JOIN cl_tx_bookings b ON b.order_id = sr.db_id
LEFT JOIN cl_tx_capacity c ON c.db_id = sr.capacity_id
WHERE sr.db_id = 12345; -- Replace with actual service request ID

-- Example 4: Timeline entries for booking
-- Query to see booking-related timeline entries

SELECT 
    t.db_id,
    t.title,
    t.update_type,
    t.c_meta,
    t.form_data
FROM cl_tx_srvc_req_tmline t
WHERE t.srvc_req_id = 12345 -- Replace with actual service request ID
  AND (t.title LIKE '%booking%' OR t.title LIKE '%Booking%')
ORDER BY t.db_id DESC;

-- Example 5: Capacity utilization check
-- Query to check capacity utilization after booking

SELECT 
    c.db_id as capacity_id,
    c.resource_id,
    c.start_time,
    c.end_time,
    c.total_capacity,
    c.available_capacity,
    c.total_cap_in_minutes,
    c.booked_cap_in_minutes,
    c.utilization,
    CASE 
        WHEN c.utilization >= 1.0 THEN 'Fully Booked'
        WHEN c.utilization >= 0.8 THEN 'High Utilization'
        WHEN c.utilization >= 0.5 THEN 'Medium Utilization'
        ELSE 'Low Utilization'
    END as utilization_status
FROM cl_tx_capacity c
WHERE c.db_id = 100; -- Replace with actual capacity ID
