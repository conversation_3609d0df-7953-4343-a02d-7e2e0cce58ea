CREATE OR REPLACE FUNCTION public.tms_create_srvc_hub_batch(form_data_ json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$

-- Declarations
declare
	-- Bare minimums
	status boolean;
	message text;
	affected_rows integer;
	validation_resp text[];
	resp_data json;
	ip_address_ text;
	user_agent_ text;
	org_id_ integer;
	usr_id_ uuid;

	-- Form data
	batch_data_ json;
	_form_data_to_create_entry json;
	
	-- temp
  	_single_entry json;
  	ins_id bigint;
  	_form_data_proto json;
  	_single_entry_creation_resp json;
  	exception_hint text;
	row_count_ int default 0;
  
	-- Output
	ins_ids bigint[];
	
	-- Verticals processing
	vertical_id_ integer;
	pin_codes_array jsonb;
	pin_codes_text text;

	-- Location validation
	state_name_ text;
	city_name_ text;
	validation_result json;

	-- Error tracking
	pre_validation_errors_list text[] := '{}';
	runtime_errors_list text[] := '{}';
	combined_errors_list text[] := '{}';
	total_records_ int := 0;

begin
	status = false;
	message = 'Internal_error';
	
	-- Extract form data
	ip_address_ = json_extract_path_text(form_data_,'ip_address');
	user_agent_ = json_extract_path_text(form_data_,'user_agent');
	org_id_ = (form_data_->>'org_id')::integer;
	usr_id_ = (form_data_->>'usr_id')::uuid;
	batch_data_ = json_extract_path(form_data_,'batch_data');
	
	-- Get vertical_id from form_data (selected vertical)
	vertical_id_ = (form_data_->>'vertical_id')::integer;

	-- Validate that vertical_id is provided 
	if vertical_id_ is null then
		raise exception 'Vertical ID is required.'
			using HINT = 'Please select a vertical before creating hubs';
	end if;
	
	-- Create form data prototype
	_form_data_proto = json_build_object(
		'ip_address', ip_address_,
		'user_agent', user_agent_,
		'org_id', org_id_,
		'usr_id', usr_id_
	);

	row_count_ = 0;

	-- Validate all entries using helper function
	validation_result = tms_validate_srvc_hub_details_batch(batch_data_, org_id_, vertical_id_);

	-- Check validation result and collect pre-validation errors
	if not (validation_result->>'status')::boolean then
		-- Extract pre-validation errors from hint
		exception_hint = validation_result->>'hint';
		-- Split the hint by ' | ' to get individual error messages
		select string_to_array(exception_hint, ' | ') into pre_validation_errors_list;
	end if;

	-- Count total records in batch_data
	select count(*) into total_records_ from json_array_elements(batch_data_);

	-- Reset counter for actual processing
	row_count_ = 0;

	-- Now process all validated hubs using original batch_data
	for _single_entry in select * from json_array_elements(batch_data_)
	loop
		row_count_ = row_count_ + 1;

		-- Get pin_codes as text and convert to array
		pin_codes_text = _single_entry->>'pin_codes';
		if pin_codes_text is not null and trim(pin_codes_text) != '' then
			-- Split by comma, trim whitespace, and convert to JSON array
			select jsonb_agg(trim(unnest)) into pin_codes_array
			  from unnest(string_to_array(pin_codes_text, ',')) as unnest;
		else
			pin_codes_array = '[]'::jsonb;
		end if;

		-- Build the complete form data entry
		_form_data_to_create_entry = _form_data_proto::jsonb ||
			json_build_object(
				'hub_name', _single_entry->>'hub_name',
				'hub_code', _single_entry->>'hub_code',
				'state', _single_entry->>'state',
				'city', _single_entry->>'city',
				'pin_codes', pin_codes_array,
				'is_active', true,
				'vertical_id', vertical_id_,
				'bulkUpdate', true
			)::jsonb;

		-- Create the service hub
		_single_entry_creation_resp = tms_create_or_update_srvc_hub(_form_data_to_create_entry, 0);

		perform pg_sleep(0.000001);-- wait 1 microsecond

		if _single_entry_creation_resp->'status' then
			ins_ids := ins_ids || (_single_entry_creation_resp->'data'#>>'{entry_id}')::bigint;
		else
			exception_hint = 'Failed row --> ' ||
 				(_single_entry#>>'{hub_name}') ||
 				' (Row ' || row_count_ || '): ' ||
 				(_single_entry_creation_resp#>>'{code}');
			runtime_errors_list = array_append(runtime_errors_list, exception_hint);
		end if;

		-- Check if this is the last record and combine all errors and raise exception
		if row_count_ = total_records_ then
			-- Combine pre-validation errors and runtime errors
			combined_errors_list =  pre_validation_errors_list || runtime_errors_list;

			if array_length(combined_errors_list, 1) > 0 then
				-- Sort errors by row number
				select array_agg(error_msg order by row_num)
				  into combined_errors_list
				  from (
					select unnest as error_msg,
						   (regexp_match(unnest, '\(Row (\d+)\)'))[1]::integer as row_num
					  from unnest(combined_errors_list)
				  ) sorted_errors;

				exception_hint = array_to_string(combined_errors_list, ' | ');
				raise exception 'Validation failed with % errors', array_length(combined_errors_list, 1)
					using HINT = exception_hint;
			end if;
		end if;
	end loop;

	status = true;
	message = 'success';
	resp_data =  json_build_object('entry_ids',ins_ids);

	return json_build_object('status',status,'code',message,'data',resp_data);

END;
$function$
;
