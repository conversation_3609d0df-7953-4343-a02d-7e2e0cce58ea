-- DROP FUNCTION public.tms_create_or_update_srvc_hub(json, int4);

CREATE OR REPLACE FUNCTION public.tms_create_or_update_srvc_hub(form_data json, entry_id integer DEFAULT 0)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
declare 
	status boolean;
	message text;
	affected_rows integer;
	resp_data json;
	--form data
	org_id_ integer;
	usr_id_ uuid;
	ip_address_ text;
	user_agent_ text;
	hub_name_ text;
	hub_code_ text;
	hub_pin_codes_ text[];
	state_ text;
	city_ text;
	vertical_id_ integer;
	is_active_ bool;
	-- Variables for conflicting hub details
	conflicting_hub_name_ text;
	conflicting_hub_code_ text;
	conflicting_pincodes_ text[];
begin
	status = false;
	message = 'Internal_error';
	--form data 
	usr_id_ = form_data->>'usr_id';
	ip_address_ = form_data->>'ip_address';  
	user_agent_ = form_data->>'user_agent';
	org_id_ = (form_data->>'org_id')::integer;
	vertical_id_ = (form_data->>'vertical_id')::integer;

	hub_name_ = form_data->>'hub_name'; 
	hub_code_ = form_data->>'hub_code'; 
	state_ = form_data->>'state'; 
	city_ = form_data->>'city'; 
	is_active_ := COALESCE((form_data->>'is_active')::boolean, true);
	hub_pin_codes_ = array(select json_array_elements_text(form_data->'pin_codes'))::text[];
    

    -- Early exit if duplicate pincode found
    select hub_name, hub_code, pincodes
      into conflicting_hub_name_, conflicting_hub_code_, conflicting_pincodes_
      from cl_tx_vertical_srvc_hubs
     where org_id = org_id_
       and vertical_id = vertical_id_
       and (pincodes && hub_pin_codes_)
       and entry_id <> id
     limit 1;

    if conflicting_hub_name_ is not null then
        status := false;
        message := 'pincode_already_exists_in_another_hub. Conflicting hub: "' ||
                   conflicting_hub_name_ || '" (Code: ' || conflicting_hub_code_ ||
                   ') with pincodes: ' || array_to_string(conflicting_pincodes_, ', ') ||
                   '. Overlapping pincodes: ' || array_to_string((
                       select array_agg(unnest)
                       from unnest(hub_pin_codes_)
                       where unnest = any(conflicting_pincodes_)
                   ), ', ');
        return json_build_object('status', status, 'code', message, 'data', null);
    end if;

	if entry_id = 0 then
	    begin
	        insert into public.cl_tx_vertical_srvc_hubs (
				"org_id", "vertical_id", "hub_name", "hub_code", "state", "city", "pincodes", "is_active", "c_by", "c_meta")
			values (
				org_id_, 
				vertical_id_,
				hub_name_,
				hub_code_,
				state_,
				city_,
				hub_pin_codes_,
				is_active_,
				usr_id_,
				row(ip_address_,user_agent_,now() at time zone 'utc')
			)
			returning id into entry_id;
			GET DIAGNOSTICS affected_rows = ROW_COUNT;
			if affected_rows = 1 then
				perform tms_create_srvc_hub_edit_history(form_data, 'CREATE', entry_id);
				status = true;
				message = 'success';
				resp_data = json_build_object('entry_id', entry_id);
			end if;
	    exception
	        when unique_violation then
	            status := false;
	            -- Get constraint name from the exception
	            if SQLERRM like '%cl_tx_vertical_srvc_hubs_unique%' and SQLERRM not like '%cl_tx_vertical_srvc_hubs_unique_1%' then
	                message := 'hub_name_already_exists. Hub name "' || hub_name_ || '" is already used by another hub.';
	            elsif SQLERRM like '%cl_tx_vertical_srvc_hubs_unique_1%' then
	                message := 'hub_code_already_exists. Hub code "' || hub_code_ || '" is already used by another hub.';
	            else
	                message := 'hub_name_or_code_already_exists';
	            end if;
	    END;
	else
	    begin
	        update public.cl_tx_vertical_srvc_hubs 
		       set "hub_name" = hub_name_,
				    "hub_code" = hub_code_,
					"state" = state_,
					"city" = city_,
					"pincodes" = hub_pin_codes_,
					"is_active" = is_active_,
					"u_by" = usr_id_,
					"u_meta" = row(ip_address_,user_agent_,now() at time zone 'utc')
		    where id = entry_id;
			GET DIAGNOSTICS affected_rows = ROW_COUNT;
			if affected_rows = 1 then
				perform tms_create_srvc_hub_edit_history(form_data, 'UPDATE', entry_id);
				status = true;
				message = 'success';
				resp_data = json_build_object('entry_id', entry_id);
			end if;
	    exception
	        when unique_violation then
	            status := false;
	            -- Get constraint name from the exception
	            if SQLERRM like '%cl_tx_vertical_srvc_hubs_unique%' and SQLERRM not like '%cl_tx_vertical_srvc_hubs_unique_1%' then
	                message := 'hub_name_already_exists. Hub name "' || hub_name_ || '" is already used by another hub.';
	            elsif SQLERRM like '%cl_tx_vertical_srvc_hubs_unique_1%' then
	                message := 'hub_code_already_exists. Hub code "' || hub_code_ || '" is already used by another hub.';
	            else
	                message := 'hub_name_or_code_already_exists';
	            end if;
	    end;
	end if;
	return json_build_object('status', status, 'code', message, 'data', resp_data);
END;
$function$
;
