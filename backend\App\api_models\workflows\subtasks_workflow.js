const {
    subtaskStatusUpdateNotificationEmail,
} = require('../queues_v2/processors/email/templates/subtask_status_update_template');
const { OTP_CHANNEL } = require('../queues_v2/processors/send_sms');
const { allQueues } = require('../queues_v2/queues');
const db_resp = require('../utils/db_resp');
const {
    parseSMSTemplateWithValues,
    getSMSTemplateDetails,
} = require('./consumer-sms-notification-template-manager');
const commonUtils = require('../utils/common');
const user_model = require('../user_model');
const { setParamsToUserModel } = require('../queues/processors/helpers');
const { callLambdaFn } = require('../utils/lambda_helpers');
const users_model = require('../users_model');

class subtasks_workflow {
    constructor(subtasks_model) {
        this.subtasks_model = subtasks_model.getFreshInstance(subtasks_model);
        this.initServiceModel();
    }

    initServiceModel() {
        this.services_model =
            require('../../api_models/services_model').getInstance();
        this.services_model.database = this.subtasks_model.db;
        this.services_model.ip_addr = this.subtasks_model.ip_address;
        this.services_model.user_agent = this.subtasks_model.user_agent_;
        this.services_model.user_context = this.subtasks_model.userContext;
        this.services_model = this.services_model.getFreshInstance(
            this.services_model
        );
    }

    initServiceModelTypeAndReqId(srvc_type_id, srvc_req_id) {
        this.initServiceModel();
        // Specific for service types
        this.services_model.srvc_type_id = srvc_type_id;
        this.services_model.srvc_req_id = srvc_req_id;
    }

    /*
        Basic rules set that would drive what to do next
        when a subtask is created or updated
        we are sending a promise so that this process
        becomes off the grid
    */

    trigger(query, entry_id, dbResp, is_customer_access = 0) {
        console.log('dbResp', dbResp);
        if (entry_id > 0) {
            // Only Resending the otp to customer and update in srvc timeline
            if (query.reSendOtp) {
                this.triggerReSendOtp(query, entry_id);
            }
            // sub tasks was update
            // this could be status update
            // this could form update
            if (query.update_type_id) {
                // this is a status update
                // this.processStatusUpdateWorkflows(entry_id, this.config_data, sbtsk_type_id, update_type_id);
                this.triggetStatusUpdateWorkflow(query, entry_id);
            }
            //HUL India - Lambda hook for deletion of subtask
            if (query.is_deleted) {
                this.triggerSbtskDeletion(query, query.srvc_req_id, entry_id);
                this.triggerProcessSbtskDeletionNotificationWorkflow(
                    query,
                    entry_id,
                    dbResp
                );
            }
            if (query.sbtsk_assignee) {
                this.triggerProcessRatingsQueueOnAssigneeChange(
                    query,
                    query.srvc_req_id,
                    entry_id
                );
                this.triggerProcessOnReAssigneeNotification(
                    query,
                    entry_id,
                    dbResp
                );
            }
        } else {
            // A subtask was created or multiple subtask where created
            // entry id resides in the dbResp

            let new_entry_id = dbResp.data.entry_id;
            let new_entry_ids = dbResp.data.entry_ids;
            let entry_ids_vs_query = dbResp.data?.entry_ids_vs_query;
            if (new_entry_id > 0) {
                let updatedQueryFrmDb = dbResp?.data?.form_data || query;
                console.log(
                    'single subtask creation updatedQueryFrmDb',
                    new_entry_id,
                    updatedQueryFrmDb
                );
                this.triggerSbtskCreationWorkflow(
                    updatedQueryFrmDb,
                    new_entry_id,
                    is_customer_access
                );
            } else if (new_entry_ids?.length > 0) {
                new_entry_ids.forEach((single_entry_id) => {
                    let updatedQueryFrmDb =
                        entry_ids_vs_query?.[single_entry_id] || query;
                    let is_sbtsk_reassignment = query.is_sbtsk_reassignment;

                    if (is_sbtsk_reassignment) {
                        const sbtskDetails = dbResp.data?.sbtsk_details || [];

                        sbtskDetails.forEach((singleSbtskDetail) => {
                            if (
                                singleSbtskDetail.sbtsk_id === single_entry_id
                            ) {
                                updatedQueryFrmDb['sbtsk_type_name'] =
                                    singleSbtskDetail?.sbtsk_details?.sbtsk_type_name;
                                updatedQueryFrmDb['sbtsk_start_day'] =
                                    singleSbtskDetail?.sbtsk_details?.sbtsk_start_day;
                                updatedQueryFrmDb['sbtsk_start_time'] =
                                    singleSbtskDetail?.sbtsk_details?.sbtsk_start_time_to_show;
                            }
                        });
                    }

                    this.triggerSbtskCreationWorkflow(
                        {
                            ...updatedQueryFrmDb,
                            sbtsk_entry_id: single_entry_id,
                            is_sbtsk_reassignment,
                        },
                        single_entry_id,
                        is_customer_access
                    );
                    //reassignment of subtask
                    if (is_sbtsk_reassignment) {
                        this.triggerSbtskReassignmentFromApi(
                            updatedQueryFrmDb,
                            updatedQueryFrmDb.srvc_req_id,
                            updatedQueryFrmDb.sbtsk_id,
                            updatedQueryFrmDb.sbtsk_assignee.key
                        );
                    }
                });
            }
        }
    }

    // trigger_v1(query,entry_id,dbResp){return new Promise((resolve,reject)=>{
    //     // setTimeout(()=>{
    //     //     console.log('Workflow runner - ',query,entry_id,dbResp);
    //     // },5000);
    //     // Getting the config data
    //     this.subtasks_model.getOverviewProto(query).then(
    //         operationResp => {
    //             if(operationResp.isSuccess()){
    //                 // we got the configuration data for this subtask type id
    //                 this.config_data = JSON.parse(operationResp.resp)?.config_data;
    //                 //update_type_id is new sbtsk_status_key
    //                 let {sbtsk_type_id, update_type_id} = query;

    //                 // console.log('Got config data');
    //                 if(entry_id > 0){
    //                     // sub tasks was update
    //                     // this could be status update
    //                     // this could form update

    //                     if(update_type_id) {
    //                         // this is a status update
    //                         this.processStatusUpdateWorkflows(entry_id, this.config_data,
    //                             sbtsk_type_id, update_type_id);
    //                     }
    //                 }
    //                 else{
    //                     // A subtask was created or multiple subtask where created
    //                     // entry id resides in the dbResp

    //                     let new_entry_id = dbResp.data.entry_id;
    //                     let new_entry_ids = dbResp.data.entry_ids;
    //                     if(new_entry_id > 0){
    //                         this.processSubTaskCreationWorkFlows(query, new_entry_id, this.config_data);
    //                     }else if(new_entry_ids?.length > 0){

    //                         new_entry_ids.forEach(single_entry_id => {
    //                             this.processSubTaskCreationWorkFlows(query, single_entry_id, this.config_data);
    //                         });
    //                     }
    //                 }
    //             }
    //             else{
    //                 console.log('Get config data failed in workflow');
    //             }
    //         }
    //     );
    //     // we always do resolve
    //     resolve();
    // })}

    triggerReSendOtp(query, entry_id) {
        let jobData = { query, entry_id };
        jobData['subtasks_model_data'] = this.subtasks_model.getSbtskModelData(
            this.subtasks_model
        );
        allQueues.WIFY_RESEND_OTP_TO_CUSTOMER.addJob(jobData);
    }

    triggetStatusUpdateWorkflow(query, entry_id) {
        let jobData = { query, entry_id };
        jobData['subtasks_model_data'] = this.subtasks_model.getSbtskModelData(
            this.subtasks_model
        );
        allQueues.WIFY_SBTSK_REQ_STATUS_UPDATE_WORKFLOW.addJob(jobData);
    }

    triggerSbtskCreationWorkflow(query, new_entry_id, is_customer_access = 0) {
        let jobData = { query, new_entry_id, is_customer_access };
        jobData['subtasks_model_data'] = this.subtasks_model.getSbtskModelData(
            this.subtasks_model
        );
        allQueues.WIFY_SBTSK_REQ_CREATION_WORKFLOW.addJob(jobData);
    }

    triggerSubtaskDeletion(query, new_entry_id) {
        let jobData = { query, new_entry_id };
        jobData['subtasks_model_data'] = this.subtasks_model.getSbtskModelData(
            this.subtasks_model
        );
        allQueues.WIFY_SBTSK_DELETION_WORKFLOW.addJob(jobData);
    }

    async processStatusUpdateWorkflows(query, entry_id, job_id = 0) {
        console.log('**** processStatusUpdateWorkflows start ****', job_id);
        //get this.config_data
        //sbtsk config_data data k liye we need to sbtsk_type_id jo query me mil jata hai.
        try {
            await this.getConfigDataFrSbtskType(query);
            console.log('Loaded config data..');
        } catch (error) {
            console.log(error);
            return;
        }
        try {
            let { automation_sbtsk_status_enable } = this.config_data;
            let { sbtsk_type_id, update_type_id, is_sbtsk_reassignment } =
                query;

            //make dummy query
            let dummyQuery = {};
            dummyQuery['sbtsk_type_id'] = sbtsk_type_id;
            dummyQuery['update_type_id'] = update_type_id;
            dummyQuery['is_sbtsk_reassignment'] = is_sbtsk_reassignment;

            console.log(
                'automation_sbtsk_status_enable',
                job_id,
                automation_sbtsk_status_enable
            );
            console.log('query', job_id, query);
            console.log('entry_id', job_id, entry_id);
            console.log('dummyQuery', job_id, dummyQuery);

            if (automation_sbtsk_status_enable) {
                console.log('reach srvcReqStatusUpdationWorkFlow', job_id);
                //process srvc req status updation workflow
                await this.srvcReqStatusUpdationWorkFlow(
                    dummyQuery,
                    entry_id,
                    this.config_data,
                    update_type_id,
                    job_id
                );
            }
            console.log('**** processStatusUpdateWorkflows end ****', job_id);

            //process sbtsk notify status update workflow (for email)
            await this.sbtaskStatusNotifyWorkFlow(
                dummyQuery,
                entry_id,
                query,
                this.config_data
            );
        } catch (error) {
            console.log('processStatusUpdateWorkflows failed', error);
        }

        // HUL India - lambda hook for technician assignment, save bridge number into service request
        try {
            let lambdaHookKey = `enable_lambda_hook_fr_${query.update_type_id}`;
            let is_sbtsk_reassignment = query.is_sbtsk_reassignment;
            if (this.config_data[lambdaHookKey] && !is_sbtsk_reassignment) {
                let lamdbaArn = `lambda_arn_fr_${query.update_type_id}`;
                // console.log('lambdaHookKey',lambdaHookKey)
                this.triggerSubtaskTechnicianAssignment(
                    query,
                    query.srvc_req_id,
                    entry_id,
                    this.config_data[lamdbaArn]
                );
            }
        } catch (error) {
            console.log('triggerSubtaskTechnicianAssignment failed', error);
            return;
        }

        if (query.update_type_id == 'sbtsk_can_postpone') {
            this.triggerProcessRatingsQueueOnAssigneeChange(
                query,
                query.srvc_req_id,
                entry_id
            );
        }
    }
    //HUL India - lambda hook for technician assignment, save bridge number into service request
    triggerSubtaskTechnicianAssignment(
        query,
        new_entry_id,
        sbtsk_id,
        lambdaArn
    ) {
        let jobData = { query, new_entry_id, sbtsk_id, lambdaArn };
        jobData['subtasks_model_data'] = this.subtasks_model.getSbtskModelData(
            this.subtasks_model
        );
        allQueues.WIFY_SBTSK_ASSIGNMENT_WORKFLOW.addJob(jobData);
    }

    //HUL India - lambda hook for technician assignment, save bridge number into service request
    async processSbtskTechnicianAssignment(
        query,
        entry_id,
        sbtsk_id,
        lambdaArn
    ) {
        try {
            console.log('**** processSbtskAssignment start ****', entry_id);
            setParamsToUserModel(user_model, this, this.subtasks_model.db);
            const srvcReqDetails = await this.services_model.getSingleEntry(
                { srvc_type_id: query.srvc_type_id },
                entry_id
            );
            console.log(
                'debug sms bridge log :: srvcReqDetails :: ',
                srvcReqDetails
            );
            const sbtskDetails = await this.subtasks_model.getSingleEntry(
                query,
                sbtsk_id
            );
            console.log('debug sms bridge log :: sbtskDetails', sbtskDetails);
            const sbtskDetailsParsed = JSON.parse(sbtskDetails.resp);
            const assignee_id =
                sbtskDetailsParsed.form_data?.form_data?.sbtsk_assignee?.key ||
                sbtskDetailsParsed.form_data?.form_data?.sbtsk_assignee?.value;
            console.log('debug sms bridge log :: assignee_id :: ', assignee_id);
            const assigneeDetails = await user_model.getUserDataById(
                {},
                assignee_id
            );
            console.log(
                'debug sms bridge log :: assigneeDetails :: ',
                assigneeDetails
            );
            if (
                !srvcReqDetails.isSuccess() ||
                !sbtskDetails.isSuccess() ||
                !assigneeDetails.isSuccess()
            ) {
                console.log(
                    'something went wrong while processSbtskTechnicianAssignment',
                    query,
                    entry_id,
                    sbtsk_id,
                    lambdaArn
                );
            }
            if (lambdaArn) {
                const lambdaARN = lambdaArn;
                const payload = {
                    srvcReqDetails: JSON.parse(srvcReqDetails.resp).form_data,
                    sbtskDetails: JSON.parse(sbtskDetails.resp).form_data,
                    assigneeDetails: JSON.parse(assigneeDetails.resp),
                };
                const params = {
                    FunctionName: lambdaARN,
                    InvocationType: 'RequestResponse',
                    LogType: 'Tail',
                    Payload: JSON.stringify({ ...payload }),
                };
                try {
                    let respData = await callLambdaFn(params);
                    let lambdaRespData = JSON.parse(respData.Payload);
                    console.log(
                        'debug sms bridge log :: lambdaRespData :: ',
                        lambdaRespData
                    );
                    if (lambdaRespData && lambdaRespData.body) {
                        let { srvc_req_form_data } = lambdaRespData.body;
                        if (srvc_req_form_data) {
                            let queryFrSrvcReq = {
                                srvc_type_id: query.srvc_type_id,
                                ...srvc_req_form_data,
                            };
                            this.initServiceModelTypeAndReqId(
                                query.srvc_type_id,
                                entry_id
                            );
                            await this.services_model.createOrUpdate(
                                queryFrSrvcReq,
                                entry_id
                            );
                        }
                    } else {
                        console.log(lambdaRespData.message);
                    }
                } catch (error) {
                    console.log(
                        'callLambdaFnForTechnicianAssignmentTrigger :: error :: ',
                        error
                    );
                }
            } else {
                console.log(
                    'lambda hook for technician assignment is disabled'
                );
            }
        } catch (error) {
            console.log('processSbtskTechnicianAssignment failed', error);
        }
    }

    //HUL India - Lambda hook for deletion of subtask
    triggerSbtskDeletion(query, new_entry_id, sbtsk_id) {
        let jobData = { query, new_entry_id, sbtsk_id };
        jobData['subtasks_model_data'] = this.subtasks_model.getSbtskModelData(
            this.subtasks_model
        );
        allQueues.WIFY_SBTSK_DELETION_WORKFLOW.addJob(jobData);
    }

    triggerSbtskDeletionWorkflow(
        query,
        entry_id,
        dbResp,
        is_customer_access = 0
    ) {
        console.log('triggerSbtskDeletionWorkflow', query);
        let new_entry_id = dbResp.data.entry_id;
        let new_entry_ids = dbResp.data.entry_ids;
        let entry_ids_vs_query = dbResp.data?.entry_ids_vs_query;
        if (new_entry_id > 0) {
            let updatedQueryFrmDb = dbResp?.data?.form_data || query;
            console.log(
                'single subtask creation updatedQueryFrmDb',
                new_entry_id,
                updatedQueryFrmDb
            );
            // this.triggerSbtskCreationWorkflow(updatedQueryFrmDb, new_entry_id, is_customer_access);
        } else if (new_entry_ids?.length > 0) {
            new_entry_ids.forEach((single_entry_id) => {
                let updatedQueryFrmDb =
                    entry_ids_vs_query?.[single_entry_id] || query;
                if (updatedQueryFrmDb.for_deletion) {
                    this.triggerSbtskDeletion(
                        updatedQueryFrmDb,
                        updatedQueryFrmDb.srvc_req_id,
                        updatedQueryFrmDb.sbtsk_id
                    );
                }
            });
        }
    }

    //HUL India - lambda hook for re assignment of subtask
    triggerSbtskReassignmentFromApi(
        query,
        new_entry_id,
        sbtsk_id,
        assignee_id
    ) {
        let jobData = { query, new_entry_id, sbtsk_id, assignee_id };
        jobData['subtasks_model_data'] = this.subtasks_model.getSbtskModelData(
            this.subtasks_model
        );
        allQueues.WIFY_SBTSK_REASSIGNMENT_WORKFLOW.addJob(jobData);
    }

    async triggerProcessRatingsQueueOnAssigneeChange(
        query,
        srvc_req_id,
        sbtsk_id
    ) {
        try {
            const sbtskOperationResp = await this.subtasks_model.getSingleEntry(
                { ...query },
                sbtsk_id
            );
            if (!sbtskOperationResp.isSuccess()) {
                return;
            }
            let sbtsk_data = JSON.parse(sbtskOperationResp.resp)?.form_data;
            const srvc_req_id_ =
                sbtsk_data?.form_data?.srvc_req_id || srvc_req_id;
            const srvc_type_id = sbtsk_data?.form_data?.srvc_type_id;
            await this.processRatingsQueue(
                { sbtsk_id, srvc_type_id, ...query },
                srvc_req_id_
            );
        } catch (e) {
            console.log('triggerProcessRatingsQueueOnAssigneeChange failed', e);
        }
    }

    triggerProcessSbtskDeletionNotificationWorkflow(query, entry_id, dbResp) {
        let jobData = { query, entry_id, dbResp };
        jobData['subtasks_model_data'] = this.subtasks_model.getSbtskModelData(
            this.subtasks_model
        );
        allQueues.WIFY_TMS_PROCESS_SUBTASK_DELETION_NOTIFICATION_WORKFLOW.addJob(
            jobData
        );
    }
    triggerProcessOnReAssigneeNotification(query, entry_id, dbResp) {
        let jobData = { query, entry_id, dbResp };
        jobData['subtasks_model_data'] = this.subtasks_model.getSbtskModelData(
            this.subtasks_model
        );
        allQueues.WIFY_TMS_PROCESS_SUBTASK_REASSIGN_NOTIFICATION_WORKFLOW.addJob(
            jobData
        );
    }

    async triggerSubtaskDeletionNotificationWorkFlow(data, job_id) {
        console.log(
            '**** process triggerSubtaskDeletionNotificationWorkFlow start **** ',
            job_id
        );
        const entry_id_vs_query_fr_deletion =
            data.entry_id_vs_query_fr_deletion;
        if (entry_id_vs_query_fr_deletion) {
            Object.keys(entry_id_vs_query_fr_deletion).map(
                (single_entry_id) => {
                    const query =
                        entry_id_vs_query_fr_deletion[single_entry_id];
                    let jobData = { ...query, sbtsk_entry_id: single_entry_id };
                    jobData['subtasks_model_data'] =
                        this.subtasks_model.getSbtskModelData(
                            this.subtasks_model
                        );
                    allQueues.WIFY_TMS_SUBTASK_DELETION_NOTIFICATION.addJob(
                        jobData
                    );
                }
            );
        }
        console.log(
            '**** process triggerSubtaskDeletionNotificationWorkFlow start **** ',
            job_id
        );
    }
    async processSbtskReassignment(
        query,
        srvc_req_id,
        sbtsk_id,
        assignee_id,
        db
    ) {
        try {
            //get old assignee id of reassign sbtsk
            const oldAssigneeId = (
                await db.tms_get_old_assignee_id_by_sbtsk_id(
                    sbtsk_id,
                    query.org_id
                )
            )?.[0].tms_get_old_assignee_id_by_sbtsk_id;
            console.log(
                '**** processSbtskReassignment start ****',
                srvc_req_id
            );
            try {
                await this.getConfigDataFrSbtskType(query);
                console.log('Loaded config data..');
            } catch (error) {
                console.log(error);
                return;
            }
            let {
                enable_lambda_hook_fr_subtask_reassignment,
                lambda_arn_fr_subtask_reassignment,
            } = this.config_data;
            const oldAssigneeDetails = await user_model.getUserDataById(
                {},
                oldAssigneeId.data
            );
            const srvcReqDetails = await this.services_model.getSingleEntry(
                { srvc_type_id: query.srvc_type_id },
                srvc_req_id
            );
            const sbtskDetails = await this.subtasks_model.getSingleEntry(
                query,
                sbtsk_id
            );
            const newAssigneeDetails = await user_model.getUserDataById(
                {},
                assignee_id
            );
            if (enable_lambda_hook_fr_subtask_reassignment) {
                const lambdaARN = lambda_arn_fr_subtask_reassignment;
                const payload = {
                    srvcReqDetails: JSON.parse(srvcReqDetails.resp).form_data,
                    sbtskDetails: JSON.parse(sbtskDetails.resp).form_data,
                    newAssigneeDetils: JSON.parse(newAssigneeDetails.resp),
                    oldAssigneeDetails: JSON.parse(oldAssigneeDetails.resp),
                };
                const params = {
                    FunctionName: lambdaARN,
                    InvocationType: 'RequestResponse',
                    LogType: 'Tail',
                    Payload: JSON.stringify({ ...payload }),
                };
                try {
                    let respData = await callLambdaFn(params);
                    let lambdaRespData = JSON.parse(respData.Payload);
                    if (lambdaRespData && lambdaRespData.body) {
                        let { srvc_req_form_data } = lambdaRespData.body;
                        if (srvc_req_form_data) {
                            let queryFrSrvcReq = {
                                srvc_type_id: query.srvc_type_id,
                                ...srvc_req_form_data,
                            };
                            this.initServiceModelTypeAndReqId(
                                query.srvc_type_id,
                                srvc_req_id
                            );
                            await this.services_model.createOrUpdate(
                                queryFrSrvcReq,
                                srvc_req_id
                            );
                        }
                    } else {
                        console.log(lambdaRespData.message);
                    }
                } catch (error) {
                    console.log(
                        'callLambdaFnForReassignmentTrigger :: error :: ',
                        error
                    );
                }
            } else {
                console.log('lambda hook for sbtsk reassignment is disabled');
            }
        } catch (error) {
            console.log('processSbtskReassignment failed', error);
        }
    }

    async processSbtskReassignmentFrBridgeRecreate(
        query,
        srvc_req_id,
        sbtsk_id,
        assignee_id,
        db,
        temp_use_new_assinee_id = false
    ) {
        try {
            //get old assignee id of reassign sbtsk
            const oldAssigneeId = (
                await db.tms_get_old_assignee_id_by_sbtsk_id(
                    sbtsk_id,
                    query.org_id
                )
            )?.[0].tms_get_old_assignee_id_by_sbtsk_id;
            console.log(
                '**** processSbtskReassignment start ****',
                srvc_req_id
            );
            try {
                await this.getConfigDataFrSbtskType(query);
                console.log('Loaded config data..');
            } catch (error) {
                console.log(error);
                return;
            }
            let {
                enable_lambda_hook_fr_subtask_reassignment,
                lambda_arn_fr_subtask_reassignment,
            } = this.config_data;
            const newAssigneeDetails = await user_model.getUserDataById(
                {},
                assignee_id
            );
            let oldAssigneeDetails;
            if (temp_use_new_assinee_id) {
                oldAssigneeDetails = newAssigneeDetails;
            } else {
                oldAssigneeDetails = await user_model.getUserDataById(
                    {},
                    oldAssigneeId.data
                );
            }
            const srvcReqDetails = await this.services_model.getSingleEntry(
                { srvc_type_id: query.srvc_type_id },
                srvc_req_id
            );
            const sbtskDetails = await this.subtasks_model.getSingleEntry(
                query,
                sbtsk_id
            );
            if (enable_lambda_hook_fr_subtask_reassignment) {
                const lambdaARN = lambda_arn_fr_subtask_reassignment;
                const payload = {
                    srvcReqDetails: JSON.parse(srvcReqDetails.resp).form_data,
                    sbtskDetails: JSON.parse(sbtskDetails.resp).form_data,
                    newAssigneeDetils: JSON.parse(newAssigneeDetails.resp),
                    oldAssigneeDetails: JSON.parse(oldAssigneeDetails.resp),
                };
                const params = {
                    FunctionName: lambdaARN,
                    InvocationType: 'RequestResponse',
                    LogType: 'Tail',
                    Payload: JSON.stringify({ ...payload }),
                };
                try {
                    let respData = await callLambdaFn(params);
                    let lambdaRespData = JSON.parse(respData.Payload);
                    if (lambdaRespData && lambdaRespData.body) {
                        let { srvc_req_form_data } = lambdaRespData.body;
                        if (srvc_req_form_data) {
                            let queryFrSrvcReq = {
                                srvc_type_id: query.srvc_type_id,
                                ...srvc_req_form_data,
                            };
                            this.initServiceModelTypeAndReqId(
                                query.srvc_type_id,
                                srvc_req_id
                            );
                            await this.services_model.createOrUpdate(
                                queryFrSrvcReq,
                                srvc_req_id
                            );
                        }
                        return true;
                    } else {
                        console.log(lambdaRespData.message);
                    }
                } catch (error) {
                    console.log(
                        'callLambdaFnForReassignmentTrigger :: error :: ',
                        error
                    );
                }
            } else {
                console.log('lambda hook for sbtsk reassignment is disabled');
            }
        } catch (error) {
            console.log('processSbtskReassignment failed', error);
        }
    }

    async processSbtskDeletion(query, srvc_req_id, sbtsk_id, db) {
        try {
            //get assignee id of deleted sbtsk
            const assigneeId = (
                await db.tms_get_sbtsk_assignee_details_frm_sbtsk_id(
                    sbtsk_id,
                    query.org_id
                )
            )?.[0].tms_get_sbtsk_assignee_details_frm_sbtsk_id;
            console.log(
                '**** processSbtskDeletionWorkflows start ****',
                sbtsk_id
            );

            try {
                await this.getConfigDataFrSbtskType(query);
                console.log('Loaded config data..');
            } catch (error) {
                console.log(error);
                return;
            }
            let {
                enable_lambda_hook_fr_subtask_deletion,
                lambda_arn_fr_subtask_deletion,
            } = this.config_data;
            const assigneeDetails = await user_model.getUserDataById(
                {},
                assigneeId.data
            );
            const srvcReqDetails = await this.services_model.getSingleEntry(
                { srvc_type_id: query.srvc_type_id },
                srvc_req_id
            );
            const sbtskDetails = await this.subtasks_model.getSingleEntry(
                query,
                sbtsk_id
            );
            if (enable_lambda_hook_fr_subtask_deletion) {
                const lambdaARN = lambda_arn_fr_subtask_deletion;
                const payload = {
                    srvcReqDetails: JSON.parse(srvcReqDetails.resp).form_data,
                    sbtskDetails: JSON.parse(sbtskDetails.resp).form_data,
                    assigneeDetails: JSON.parse(assigneeDetails.resp),
                };
                const params = {
                    FunctionName: lambdaARN,
                    InvocationType: 'RequestResponse',
                    LogType: 'Tail',
                    Payload: JSON.stringify({ ...payload }),
                };
                try {
                    let respData = await callLambdaFn(params);
                    let lambdaRespData = JSON.parse(respData.Payload);
                    if (lambdaRespData && lambdaRespData.body) {
                        let { srvc_req_form_data } = lambdaRespData.body;
                        if (srvc_req_form_data) {
                            let queryFrSrvcReq = {
                                srvc_type_id:
                                    payload.srvcReqDetails.srvc_type_id,
                                ...srvc_req_form_data,
                            };
                            this.initServiceModelTypeAndReqId(
                                query.srvc_type_id,
                                srvc_req_id
                            );
                            await this.services_model.createOrUpdate(
                                queryFrSrvcReq,
                                srvc_req_id
                            );
                        }
                    } else {
                        console.log(lambdaRespData.message);
                    }
                } catch (error) {
                    console.log(
                        'callLambdaFnForSbtskDeletionTrigger :: error :: ',
                        error
                    );
                }
            } else {
                console.log('lambda hook for sbtsk deletion is disabled');
            }
            if (query?.is_deleted) {
                this.triggerProcessRatingsQueueOnSbtskDeletion(
                    { ...query, subtaskIdsToBeDeleted: [sbtsk_id] },
                    {}
                );
            }
        } catch (error) {
            console.log('processSbtskDeletion failed', error);
        }
    }

    async processOtpAutomation(query, new_entry_id, db) {
        //get this.config_data
        //sbtsk config_data data k liye we need to sbtsk_type_id jo query me mil jata hai.
        // console.log('Got config data inside subtask workflow',this.service_config_data);
        try {
            const sbtskResp = (
                await db.tms_get_sbtsk_details_by_id(query.org_id, new_entry_id)
            )?.[0].tms_get_sbtsk_details_by_id;
            if (sbtskResp.status) {
                let sbtskDetails = sbtskResp.data;
                query['sbtsk_type_id'] = sbtskDetails.sbtsk_type_id;
                query['srvc_req_id'] = sbtskDetails.srvc_req_id;
            } else {
                console.log('sbtskResp failed');
            }
        } catch (error) {
            console.log(error);
            return;
        }

        console.log('inside send OTP Func');
        try {
            await this.getConfigDataFrSbtskType(query);
            console.log('Loaded config data..');
        } catch (error) {
            console.log(error);
            return;
        }

        let { sbtsk_type_id, update_type_id, srvc_req_id } = query;
        let sms_prefix = 'otp_sms';
        let sms_template_val = this.config_data[`${sms_prefix}_template`];
        let sms_templates = getSMSTemplateDetails(
            'configure_consumer_otp_verification',
            sms_template_val
        );
        // let sms_template = 'Dear %cust_name%, %otp% is your OTP for your %CUSTOM_VAR_1% by Installco Wify. Please share this with the %CUSTOM_VAR_2%  to %CUSTOM_VAR_3%  the %CUSTOM_VAR_4%.';
        if (!sms_templates) {
            const org_config_data = await this.getOrgLevelSettingsData();
            if (org_config_data.consumer_otp_sms_templates) {
                const customStatusWiseTemplates =
                    org_config_data.consumer_otp_sms_templates?.find(
                        (item) => item.value == sms_prefix
                    )?.templates;
                sms_templates = customStatusWiseTemplates?.find(
                    (item) => item.value == sms_template_val
                );
            }
            // console.log("org_config_data",org_config_data);
        }

        let custom_fields_in_template = sms_templates?.custom_fields_meta;
        let custom_fields_data = {};
        custom_fields_in_template?.map((singleCustomField) => {
            custom_fields_data[`%${singleCustomField.key}%`] =
                this.config_data[`${sms_prefix}_${singleCustomField.key}`];
        });
        // console.log("custom_fields_data",JSON.stringify(custom_fields_data));
        let final_template = parseSMSTemplateWithValues(
            sms_template_val,
            custom_fields_data
        );

        //process srvc req status updation workflow
        try {
            const form_data = await this.services_model.getDataFrNotification(
                {},
                srvc_req_id
            );
            // console.log("form_data",JSON.stringify(form_data));
            let static_data_fields = {};
            Object?.keys(form_data).map((singleFormDataKey) => {
                static_data_fields[`%${singleFormDataKey}%`] =
                    form_data[singleFormDataKey];
            });

            final_template = parseSMSTemplateWithValues(
                final_template,
                static_data_fields
            );
            // console.log("final_template",final_template);
            // //check if final_template wich technician_name and scheduled_date exists
            let customerNameOrOtpIsUndefined =
                final_template?.includes('%cust_name%') ||
                final_template?.includes('%otp%');

            if (!customerNameOrOtpIsUndefined) {
                //send sms to consumer
                let sendSMSdata = {
                    to: form_data.cust_mobile,
                    message: final_template,
                    channel: OTP_CHANNEL,
                    user_info: {
                        ...this.services_model.getServicesModelData(
                            this.services_model,
                            true
                        ),
                        tms_display_code: form_data?.display_code,
                        ext_order_id: form_data?.ext_order_id,
                        configure_consumer_otp: form_data?.otp,
                    },
                    notificationTimeline: {
                        successMessage: 'OTP has been sent to customer',
                        failedMessage: 'OTP has not been sent to customer',
                        entry_id: srvc_req_id,
                        final_template: '',
                    },
                };

                allQueues.WIFY_SEND_SMS.addJob(sendSMSdata);
                // console.log('consumer_notification_sms -> ',final_template);
            } else {
                //Notification data update on timeline
                await this.notificationDataUpdateOnTimeline(
                    srvc_req_id,
                    true,
                    'Customer not notified as no subtask was created.',
                    'SMS sending failed to customer'
                );
            }
        } catch (error) {
            //swallow
            console.log('SMS notification failed', error);
        }
    }

    async sbtaskStatusNotifyWorkFlow(dummyQuery, entry_id, query, config_data) {
        let { update_type_id, usr_id } = query; //This usr_id is basically onfield user

        let sbtsk_status_notify_authorities = `sbtsk_status_${update_type_id}_notify_authorties`;
        let notify_authorities_usr_roles =
            config_data[sbtsk_status_notify_authorities];
        if (
            notify_authorities_usr_roles == undefined ||
            !(notify_authorities_usr_roles.length > 0)
        ) {
            return;
        }
        const sbtskOperationResp = await this.subtasks_model.getSingleEntry(
            dummyQuery,
            entry_id
        );
        if (!sbtskOperationResp.isSuccess()) {
            return;
        }
        let sbtsk_data = JSON.parse(sbtskOperationResp.resp)?.form_data;
        let srvc_type_id = sbtsk_data?.form_data?.srvc_type_id;
        let srvc_req_id = sbtsk_data?.form_data?.srvc_req_id;
        this.initServiceModelTypeAndReqId(srvc_type_id, srvc_req_id);
        const srvcReqOperationResp = await this.services_model.getSingleEntry(
            {},
            srvc_req_id
        );
        if (!srvcReqOperationResp.isSuccess()) {
            return;
        }
        const pre_srvc_req_form_data = JSON.parse(
            srvcReqOperationResp.resp
        )?.form_data;
        let srvc_req_form_data = JSON.parse(srvcReqOperationResp.resp)
            ?.form_data?.form_data;

        //get srvc_authorities from srvc_type config_data
        const srvc_type_config_data_resp =
            await this.services_model.getSrvcTypeConfigData({});
        if (!srvc_type_config_data_resp.isSuccess()) {
            return;
        }
        let srvc_authorities = JSON.parse(srvc_type_config_data_resp.resp)
            ?.config_data?.srvc_authorities;
        let prvdr_authorities = JSON.parse(srvc_type_config_data_resp.resp)
            ?.sp_authorities_config_data?.authority_id;

        //get org_details for notify
        const orgDetailsResp = await this.subtasks_model.getOrgDetails(
            srvc_req_form_data?.org_id
        );
        if (!orgDetailsResp.isSuccess()) {
            return;
        }
        let org_details = JSON.parse(orgDetailsResp.resp);

        //get users_details for notify
        const onFieldUsrResp = await this.subtasks_model.getUserDetails(usr_id);
        if (!onFieldUsrResp.isSuccess()) {
            return;
        }
        let onFields_usr_details = JSON.parse(onFieldUsrResp.resp);
        // console.log("onFields_usr_details",onFields_usr_details);

        const host = process.env.FRONTEND_URL || 'tms.wify.co.in';

        let target_link_acc_to_org_type =
            org_details?.org_type == 'ORG_TYPE_SRVC_PRVDR'
                ? `https://${host}/customer-requests?query=${pre_srvc_req_form_data.title}`
                : `https://${host}/services/${srvc_type_id}?query=${pre_srvc_req_form_data.title}`;

        let sbtsk_notify_data = {
            subtask_type_name: sbtsk_data?.sbtsk_type,
            status_title: this.getSbtskStatusWithExceptions(
                sbtsk_status_notify_authorities,
                sbtsk_data
            ),
            service_req_title: sbtsk_data?.form_data?.sbtsk_ticket_id,
            org_details: org_details,
            to_name: onFields_usr_details?.user_name || 'Technician',
            targetLink: target_link_acc_to_org_type,
        };
        let emailSuccessOrFailureDataFrTimeline = {};

        for (const single_notify_authorties_usr_roles of notify_authorities_usr_roles) {
            if (
                !srvc_authorities?.includes(
                    single_notify_authorties_usr_roles
                ) &&
                !prvdr_authorities?.includes(single_notify_authorties_usr_roles)
            ) {
                continue;
            }

            let srvc_authority_key = `authority_${single_notify_authorties_usr_roles}`;
            let authorities_user_id = srvc_req_form_data[srvc_authority_key];
            let emailQuery = {};

            if (authorities_user_id) {
                const authoritiesUserResp =
                    await this.subtasks_model.getUserDetails(
                        authorities_user_id
                    );

                if (authoritiesUserResp.isSuccess()) {
                    let authoritiesUserDetails = JSON.parse(
                        authoritiesUserResp.resp
                    );
                    let authorities_user_data = {
                        from_name: authoritiesUserDetails?.user_name,
                    };

                    let notify_data = {
                        ...sbtsk_notify_data,
                        ...authorities_user_data,
                    };

                    let sendEmailData = {
                        to: authoritiesUserDetails?.user_email,
                        subject: `${onFields_usr_details?.user_name?.split(' ')[0]} ${this.getSbtskStatusWithExceptions(sbtsk_status_notify_authorities, sbtsk_data)} for ${sbtsk_data?.sbtsk_type} to ${sbtsk_data?.form_data?.sbtsk_ticket_id}`,
                        message:
                            subtaskStatusUpdateNotificationEmail(notify_data),
                        cc: '',
                        bcc: '',
                        org_id: query?.org_id,
                        usr_id: query?.usr_id,
                        ip_address: query?.ip_address,
                        user_agent: query?.user_agent,
                    };

                    emailQuery[srvc_authority_key] =
                        authoritiesUserDetails?.form_data?.user_name;

                    try {
                        allQueues.WIFY_SEND_EMAIL.addJob(sendEmailData);
                        emailSuccessOrFailureDataFrTimeline['success'] =
                            emailSuccessOrFailureDataFrTimeline['success']
                                ? {
                                      ...emailSuccessOrFailureDataFrTimeline[
                                          'success'
                                      ],
                                      ...emailQuery,
                                  }
                                : emailQuery;
                    } catch (error) {
                        emailSuccessOrFailureDataFrTimeline['failure'] =
                            emailSuccessOrFailureDataFrTimeline['failure']
                                ? {
                                      ...emailSuccessOrFailureDataFrTimeline[
                                          'failure'
                                      ],
                                      ...emailQuery,
                                  }
                                : emailQuery;
                    }
                }
            }
        }

        for (const singleEmailSuccessOrFailureDataFrTimeline of Object.keys(
            emailSuccessOrFailureDataFrTimeline
        )) {
            if (
                emailSuccessOrFailureDataFrTimeline[
                    singleEmailSuccessOrFailureDataFrTimeline
                ]
            ) {
                let srvcTypeId = srvc_type_id;
                let srvcReqId = srvc_req_id;
                let timelineMsg =
                    singleEmailSuccessOrFailureDataFrTimeline === 'success'
                        ? 'Notified authority via email'
                        : 'Failed to notify authority via email';
                let formData =
                    emailSuccessOrFailureDataFrTimeline[
                        singleEmailSuccessOrFailureDataFrTimeline
                    ];

                this.services_model.createTimelineforSrvcReq(
                    formData,
                    srvcTypeId,
                    srvcReqId,
                    'UPDATE',
                    timelineMsg
                );
            }
        }
    }

    getSbtskStatusWithExceptions = (
        sbtsk_status_notify_authorities,
        sbtsk_data
    ) => {
        if (
            sbtsk_status_notify_authorities ==
            'sbtsk_status_sbtsk_can_postpone_notify_authorties'
        ) {
            return 'Postponed';
        }
        if (
            sbtsk_status_notify_authorities ==
            'sbtsk_status_sbtsk_can_reject_notify_authorties'
        ) {
            return 'Rejected';
        }
        return sbtsk_data?.status?.title;
    };

    async srvcReqStatusUpdationWorkFlow(
        dummyQuery,
        entry_id,
        config_data,
        currentNewStatusKey,
        job_id = 0
    ) {
        if (currentNewStatusKey == undefined || currentNewStatusKey == '') {
            currentNewStatusKey = 'open';
        }
        const operationResp = await this.subtasks_model.getSingleEntry(
            dummyQuery,
            entry_id
        );
        if (operationResp.isSuccess()) {
            let sbtsk_form_data = JSON.parse(operationResp.resp)?.form_data
                ?.form_data;
            let srvc_type_id = sbtsk_form_data.srvc_type_id;
            let srvc_req_id = sbtsk_form_data.srvc_req_id;
            this.initServiceModelTypeAndReqId(srvc_type_id, srvc_req_id);

            //make auto_srvc_req_status_key
            let auto_srvc_req_status_key =
                srvc_type_id + '_' + currentNewStatusKey;
            // console.log("auto_srvc_req_status_key-", auto_srvc_req_status_key);

            let auto_srvc_status_config_data =
                config_data
                    ?.auto_srvc_status_movement_rule_fr_sbtsk_status_change[
                    auto_srvc_req_status_key
                ];
            let srvc_req_status = auto_srvc_status_config_data?.srvc_req_status;

            console.log(
                'srvcReqStatusUpdationWorkFlow auto_srvc_req_status_key',
                job_id,
                auto_srvc_req_status_key
            );
            console.log(
                'srvcReqStatusUpdationWorkFlow srvc_req_status',
                job_id,
                srvc_req_status
            );
            console.log(
                'srvcReqStatusUpdationWorkFlow auto_srvc_status_config_data',
                job_id,
                auto_srvc_status_config_data
            );
            console.log(
                'srvcReqStatusUpdationWorkFlow srvc_req_id srvc_type_id',
                job_id,
                srvc_req_id,
                srvc_type_id
            );

            if (auto_srvc_status_config_data && srvc_req_status) {
                console.log(
                    'srvcReqStatusUpdationWorkFlow start job_id',
                    job_id
                );
                //update srvc_req status
                const is_sbtsk_reassignment = dummyQuery.is_sbtsk_reassignment;
                await this.moveServiceRequestToStatus(
                    {},
                    srvc_req_id,
                    srvc_type_id,
                    srvc_req_status,
                    job_id,
                    entry_id,
                    is_sbtsk_reassignment
                );
                console.log('srvcReqStatusUpdationWorkFlow end job_id', job_id);
            }
        } else {
            console.log(
                'srvcReqStatusUpdationWorkFlow getSingleEntry failed',
                job_id
            );
        }
    }

    async processSubTaskCreationWorkFlows(
        query,
        new_entry_id,
        is_customer_access,
        db
    ) {
        //Get sbtsk_type_id, srvc_req_id by new_entry_id (new_entry_id is sbtsk_db_id)
        try {
            const sbtskResp = (
                await db.tms_get_sbtsk_details_by_id(query.org_id, new_entry_id)
            )?.[0].tms_get_sbtsk_details_by_id;
            if (sbtskResp.status) {
                let sbtskDetails = sbtskResp.data;
                query['sbtsk_type_id'] = sbtskDetails.sbtsk_type_id;
                query['srvc_req_id'] = sbtskDetails.srvc_req_id;
                query['send_closure_code'] = sbtskDetails.send_closure_code;
            } else {
                console.log('sbtskResp failed');
            }
        } catch (error) {
            console.log(error);
            return;
        }
        let {
            sbtsk_type_id,
            srvc_req_id,
            srvc_type_id,
            send_closure_code,
            update_type_id,
        } = query;
        //get this.config_data
        try {
            await this.getConfigDataFrSbtskType(query);
            console.log('Loaded config data..');
        } catch (error) {
            console.log(error);
            return;
        }

        let {
            configure_consumer_otp_verification,
            enable_otp_verification_for,
        } = this.config_data;
        // from config_data check if status movement to be done for this subtask_type_id
        this.initServiceModelTypeAndReqId(srvc_type_id, srvc_req_id);
        const operationResp =
            await this.services_model.getSrvcTypeConfigData(query);
        if (operationResp.isSuccess()) {
            // we got the configuration data for this service type id
            this.service_config_data = JSON.parse(
                operationResp.resp
            )?.config_data;
            // console.log('Got config data inside subtask workflow',this.service_config_data);
            let auto_status_full_prefix = `auto_status_change_fr_subtask_${sbtsk_type_id}`;
            let auto_status_is_enabled =
                this.service_config_data[`${auto_status_full_prefix}_enabled`];
            let auto_status_status_key =
                this.service_config_data[
                    `${auto_status_full_prefix}_status_key`
                ];
            let lambdaHookKey = update_type_id
                ? `enable_lambda_hook_fr_${update_type_id}`
                : `enable_lambda_hook_fr_open`;
            let is_sbtsk_reassignment = query.is_sbtsk_reassignment;
            if (!is_sbtsk_reassignment && this.config_data[lambdaHookKey]) {
                let lamdbaArn = update_type_id
                    ? `lambda_arn_fr_${update_type_id}`
                    : 'lambda_arn_fr_open';
                await this.processSbtskTechnicianAssignment(
                    query,
                    query.srvc_req_id,
                    new_entry_id,
                    this.config_data[lamdbaArn]
                );
            }

            if (auto_status_is_enabled && auto_status_status_key) {
                await this.moveServiceRequestToStatus(
                    query,
                    srvc_req_id,
                    srvc_type_id,
                    auto_status_status_key,
                    0,
                    0,
                    is_sbtsk_reassignment
                );
            }

            // check for onfield task auto status movement
            let isSubTaskOnField = this.config_data?.sbtsk_is_onfield;
            let auto_status_fr_on_field_full_prefix = `auto_status_change_fr_on_field`;
            let auto_status_fr_on_field_status_key =
                this.service_config_data[
                    `${auto_status_fr_on_field_full_prefix}_status_key`
                ];
            if (auto_status_fr_on_field_status_key && isSubTaskOnField) {
                await this.moveServiceRequestToStatus(
                    query,
                    srvc_req_id,
                    srvc_type_id,
                    auto_status_fr_on_field_status_key,
                    0,
                    0,
                    is_sbtsk_reassignment
                );
            }
            //status update workflows
            await this.processStatusUpdateWorkflows(query, new_entry_id);

            if (send_closure_code !== false) {
                let generateOtpFrSrvcReq = (
                    await db.tms_helper_get_if_srvc_req_has_otp(srvc_req_id)
                )?.[0].tms_helper_get_if_srvc_req_has_otp;

                if (
                    configure_consumer_otp_verification &&
                    enable_otp_verification_for?.includes(
                        parseInt(srvc_type_id)
                    ) &&
                    generateOtpFrSrvcReq
                ) {
                    // console.log("Pushing OTP in srvc_req");
                    let otp = commonUtils.genRandomFourDigitNumbers();
                    db.tms_update_otp_in_srvc_req_form_data(
                        srvc_type_id,
                        srvc_req_id,
                        otp
                    ).then(async (resp) => {
                        var dbResponse = new db_resp(
                            resp[0].tms_update_otp_in_srvc_req_form_data
                        );

                        if (!dbResponse.status) {
                            console.log(
                                'OTP Push in srvc req congif data failed'
                            );
                        } else {
                            await this.processOtpAutomation(
                                query,
                                new_entry_id,
                                db
                            );
                        }
                    });
                }
            }
        } else {
            console.log('Get config data failed in workflow');
        }

        if (this.config_data?.notify_subtask_assignee_on_new_assignment) {
            //send notification on new subtask creation
            if (query?.is_sbtsk_reassignment) {
                this.processSbtskReassignNotification(query);
            } else {
                this.processNewSbtskCreationNotification(query);
            }
        }

        if (query.sbtsk_assignee) {
            try {
                await this.processRatingsQueue(
                    { sbtsk_id: new_entry_id, ...query },
                    srvc_req_id
                );
            } catch (e) {
                console.log('processRatingsQueue failed ', e);
            }
        }
    }

    processNewSbtskCreationNotification = (query) => {
        let jobData = { ...query };
        jobData['subtasks_model_data'] = this.subtasks_model.getSbtskModelData(
            this.subtasks_model
        );
        console.log(
            'processNewSbtskCreationNotification :: jobData :: ',
            jobData
        );

        allQueues.WIFY_NEW_SBTSK_CREATION_NOTIFICATION.addJob(jobData);
    };
    processSbtskReassignNotification = (query) => {
        let jobData = { ...query };
        jobData['subtasks_model_data'] = this.subtasks_model.getSbtskModelData(
            this.subtasks_model
        );
        allQueues.WIFY_SBTSK_REASSIGN_NOTIFICATION.addJob(jobData);
    };

    async triggerNotificationWorkFlow(data) {
        try {
            const entry_ids_vs_query = data.entry_ids_vs_query;
            if (entry_ids_vs_query) {
                for (const single_entry_id in entry_ids_vs_query) {
                    if (entry_ids_vs_query.hasOwnProperty(single_entry_id)) {
                        const query = entry_ids_vs_query[single_entry_id];
                        if (
                            query?.sbtsk_form_data
                                ?.notify_subtask_assignee_on_new_assignment
                        ) {
                            let jobData = {
                                ...query,
                                sbtsk_entry_id: single_entry_id,
                            };
                            jobData['subtasks_model_data'] =
                                this.subtasks_model.getSbtskModelData(
                                    this.subtasks_model
                                );
                            console.log(
                                'triggerNotificationWorkFlow :: jobData :: ',
                                jobData
                            );
                            allQueues.WIFY_NEW_SBTSK_CREATION_NOTIFICATION.addJob(
                                jobData
                            );
                        }
                        await this.processRatingsQueue(
                            { sbtsk_id: single_entry_id, ...query },
                            query.srvc_req_id
                        );
                    }
                }
            }
        } catch (error) {
            console.log('triggerNotificationWorkFlow failed', error);
        }
    }

    async triggerProcessRatingsQueueOnSbtskDeletion(query, respData) {
        try {
            let subtaskIdsToBeDeleted =
                query?.calendarObj?.subtaskIdsToBeDeleted ||
                query?.subtaskIdsToBeDeleted;

            if (subtaskIdsToBeDeleted) {
                query['is_deleted'] = true;
                for (let single_sbtsk_id of subtaskIdsToBeDeleted) {
                    await this.processRatingsQueue(
                        { sbtsk_id: single_sbtsk_id, ...query },
                        query.srvc_req_id
                    );
                }
            }
        } catch (error) {
            console.log(
                'triggerProcessRatingsQueueOnSbtskDeletion failed ',
                error
            );
        }
    }

    async processRatingsQueue(query, entry_id) {
        console.log('processRatingsQueue start', entry_id);
        let form_data = { ...query };
        form_data['role_id'] = query.role_id;
        form_data['selected_user_fr_rating'] = query?.sbtsk_assignee?.value;
        form_data['srvc_req_id'] = entry_id;
        form_data['rating_type'] = 'assignees';
        let jobData = { form_data, entry_id };
        jobData['services_model_data'] = this.subtasks_model.getSbtskModelData(
            this.subtasks_model
        );
        try {
            allQueues.WIFY_PROCESS_RATINGS_QUEUE.addJob(jobData);
        } catch (e) {
            console.log(
                `process ratings queue failed for sbtsk_id ${query?.sbtsk_id}`,
                e
            );
        }
        console.log('processRatingsQueue end', entry_id);
    }
    async processSubtaskDeletionNotificationWorkflows(
        query,
        entry_id,
        dbResp,
        db,
        job_id = 0
    ) {
        console.log(
            '**** processSubtaskDeletionNotificationWorkflows start ****',
            job_id
        );
        try {
            if (query?.sbtsk_type_id && query?.srvc_req_id) {
                const sbtskResp = (
                    await db.tms_get_sbtsk_details_by_id(query.org_id, entry_id)
                )?.[0].tms_get_sbtsk_details_by_id;
                if (sbtskResp.status) {
                    let sbtskDetails = sbtskResp.data;
                    query['sbtsk_type_id'] = sbtskDetails.sbtsk_type_id;
                    query['srvc_req_id'] = sbtskDetails.srvc_req_id;
                } else {
                    console.log('sbtskResp failed');
                }
            }
            let { sbtsk_type_id, srvc_req_id, srvc_type_id } = query;
            //get this.config_data
            try {
                await this.getConfigDataFrSbtskType(query);
                console.log('Loaded config data..');
            } catch (error) {
                console.log(error);
                return;
            }

            //make dummy query
            let dummyQuery = { ...dbResp.data };

            const { entry_id_vs_query_fr_deletion } = dummyQuery;
            // console.log("processSubtaskDeletionNotificationWorkflows",dummyQuery);
            // console.log("processSubtaskDeletionNotificationWorkflows entry_id_vs_query_fr_deletion",entry_id_vs_query_fr_deletion);

            if (
                entry_id_vs_query_fr_deletion &&
                this.config_data?.notify_assignee_on_subtask_deletion
            ) {
                //process process sbtsk deletion notify workflow
                await this.triggerSubtaskDeletionNotificationWorkFlow(
                    dummyQuery,
                    job_id
                );
            }
        } catch (error) {
            console.log(
                'processSubtaskDeletionNotificationWorkflows failed',
                error
            );
        }
    }
    async processSubtaskReassignNotificationWorkflows(
        query,
        entry_id,
        dbResp,
        db,
        job_id = 0
    ) {
        console.log(
            '**** processSubtaskReassignNotificationWorkflows start ****',
            job_id
        );
        try {
            if (query?.sbtsk_type_id) {
                const sbtskResp = (
                    await db.tms_get_sbtsk_details_by_id(query.org_id, entry_id)
                )?.[0].tms_get_sbtsk_details_by_id;
                if (sbtskResp.status) {
                    let sbtskDetails = sbtskResp.data;
                    query['sbtsk_type_id'] = sbtskDetails.sbtsk_type_id;
                    query['srvc_req_id'] = sbtskDetails.srvc_req_id;
                } else {
                    console.log('sbtskResp failed');
                }
            }

            //get this.config_data
            try {
                await this.getConfigDataFrSbtskType(query);
            } catch (error) {
                console.log(
                    'processSubtaskReassignNotificationWorkflows error',
                    error
                );
                return;
            }
            const entry_id_vs_query_fr_reassign =
                dbResp?.data?.entry_id_vs_query_fr_reassign?.[entry_id];
            //make dummy query
            let dummyQuery = { ...query };
            dummyQuery['sbtsk_id'] = entry_id_vs_query_fr_reassign?.entry_id;
            dummyQuery['sbtsk_entry_id'] =
                entry_id_vs_query_fr_reassign?.entry_id;
            dummyQuery['sbtsk_type_name'] =
                entry_id_vs_query_fr_reassign?.sbtsk_type_name;
            dummyQuery['sbtsk_start_time'] =
                entry_id_vs_query_fr_reassign?.sbtsk_start_time;
            dummyQuery['srvc_req_address'] =
                entry_id_vs_query_fr_reassign?.srvc_req_address;
            dummyQuery['sbtsk_ticket_id'] =
                entry_id_vs_query_fr_reassign?.sbtsk_ticket_id;
            // console.log(
            //     'processSubtaskReassignNotificationWorkflows',
            //     dummyQuery
            // );
            // console.log("processSubtaskReassignNotificationWorkflows entry_id_vs_query_fr_deletion",entry_id_vs_query_fr_deletion);

            if (this.config_data?.notify_subtask_assignee_on_new_assignment) {
                //process process sbtsk deletion notify workflow
                await this.processSbtskReassignNotification(dummyQuery);
            }
        } catch (error) {
            console.log(
                'processSubtaskReassignNotificationWorkflows failed',
                error
            );
        }
    }
    async getConfigDataFrSbtskType(query) {
        console.log('getConfigDataFrSbtskType query', query);
        const operationResp = await this.subtasks_model.getOverviewProto(query);
        if (operationResp.isSuccess()) {
            this.config_data = JSON.parse(operationResp.resp)?.config_data;
            if (this.config_data) {
                return;
            }
        }
        throw 'Config data not available';
    }

    async moveServiceRequestToStatus(
        query,
        srvc_req_id,
        srvc_type_id,
        auto_status_status_key,
        job_id = 0,
        entry_id = 0,
        is_sbtsk_reassignment = false
    ) {
        let status_update_query = {};
        status_update_query['sbtsk_db_id'] = query.sbtsk_entry_id || entry_id;
        status_update_query['new_status'] = auto_status_status_key;
        if (is_sbtsk_reassignment) {
            status_update_query['is_sbtsk_reassignment'] =
                is_sbtsk_reassignment;
        }
        console.log(
            'moveServiceRequestToStatus job_id',
            job_id,
            status_update_query,
            srvc_req_id,
            entry_id
        );
        await this.services_model.createOrUpdate(
            status_update_query,
            srvc_req_id,
            0,
            0,
            job_id
        );
    }

    async notificationDataUpdateOnTimeline(
        entry_id,
        notification_data,
        final_template,
        title
    ) {
        console.log('updating timeline');
        let notification_query = {};
        notification_query['notification_data'] = notification_data;
        notification_query['comment'] = final_template;
        notification_query['title'] = title;
        await this.services_model.createOrUpdate(notification_query, entry_id);
    }

    getOrgID() {
        return users_model.getOrgId(this.subtasks_model.user_context);
    }

    async getOrgLevelSettingsData() {
        let organisations_model = require('../organisations_model');
        let orgId = this.getOrgID();
        organisations_model = organisations_model.getFreshInstance(
            this.subtasks_model
        );
        const operationResp = await organisations_model.getSingleEntry(orgId);
        // console.log("operationResp",operationResp);
        if (operationResp.isSuccess()) {
            let orgConfigData = JSON.parse(operationResp.resp);
            return orgConfigData;
        }
        console.log('subtask_workflow getOrgLevelSettingsData error');
        throw 'Org_level_setting data not available';
    }

    getFreshInstance(model) {
        const clonedInstance = new subtasks_workflow(model.subtasks_model);
        Object.assign(clonedInstance, model);
        return clonedInstance;
    }
}

module.exports.default = subtasks_workflow;
