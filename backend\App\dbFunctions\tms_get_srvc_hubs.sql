-- DROP FUNCTION public.tms_get_srvc_hubs(int4, int4, int4, int4, json, text);

CREATE OR REPLACE FUNCTION public.tms_get_srvc_hubs(org_id_ integer, vertical_id integer, page_no integer, page_size integer, filter_ json, search_query text)
 RETURNS json
 LANGUAGE plpgsql
AS $function$

-- Declarations
declare 
	status boolean;
	message text;
	resp_data json;
	pagination json;
	total integer;
	affected_rows integer;

	validate_resp uuid[];
	data_ json;
	filter_search_query text;
	filter_state_ json;
	filter_state_list text[];
	filter_is_active_ json;
	is_active_array boolean[];
    matching_hub_json json;
   	
   	temp_hub_id int;
	matching_hub_id int[];
	vertical_id_ int;

begin

	status = false;
	message = 'Internal_error';
	
	vertical_id_ = vertical_id::int;
	filter_state_ = json_extract_path(filter_,'state_list');
	filter_state_list = array( select json_array_elements_text(filter_state_) );
	filter_is_active_ = json_extract_path(filter_,'is_active');
	is_active_array = array( select json_array_elements_text(filter_is_active_) );

	if search_query !='' then
		filter_search_query = concat('%',search_query,'%');								    			
	end if;


	matching_hub_json = array_to_json(array(
        select jsonb_build_object( 
			   		'id', hub_.id ,
			     	'full_count', count(hub_.id) OVER()
			   )
	      from public.cl_tx_vertical_srvc_hubs as hub_
		 where hub_.org_id = org_id_ 
		   and (
		   			search_query = ''
		   			or hub_.city ilike filter_search_query
					or hub_.state ilike filter_search_query
		            or hub_.hub_name ilike filter_search_query
					or hub_.hub_code ilike filter_search_query
					or exists (
					              select 1 
					                from unnest(hub_.pincodes) as p 
					               where p ilike filter_search_query
					         )
		   	   )
		   and ( 
		   			cardinality(filter_state_list) = 0 
		   		 	or hub_.state = any( filter_state_list )
		   	   )
		   and ( 
		   			cardinality(is_active_array) = 0 
		   		 	or hub_.is_active = any( is_active_array )
		   	   )

		   and  hub_.vertical_id = vertical_id_
		 order by hub_.hub_name asc 
		 limit page_size
		offset ( (page_no - 1) * page_size )
	));


	for single_hub_index in 0..json_array_length(matching_hub_json) - 1 loop
	  temp_hub_id = json_extract_path_text(matching_hub_json -> single_hub_index,'id')::integer;
	  total = json_extract_path_text(matching_hub_json -> single_hub_index,'full_count')::integer;
	  matching_hub_id = array_append(matching_hub_id,temp_hub_id);
    end loop;
   
   
	data_ = array_to_json(array(
    	select jsonb_build_object(
    				'id', hub_.id ,
    				'hub_name', hub_.hub_name ,
    				'hub_code', hub_.hub_code ,
    				'state', hub_.state,
					'city', hub_.city ,
					'pin_codes', hub_.pincodes 
    		    ) 
	      from public.cl_tx_vertical_srvc_hubs as hub_
		 where hub_.id = any( matching_hub_id )
		   and hub_.vertical_id = vertical_id_
		 order 
            by hub_.hub_name asc 
	));
			    
    pagination = jsonb_build_object('total', total  ,'current', page_no);
    resp_data =  jsonb_build_object('pagination',pagination,'data',data_);
   
	--Sending the below by default
    status  = true;
    message = 'success';

	return json_build_object('status',status,'code',message,'data',resp_data);
   
END;
$function$
;
