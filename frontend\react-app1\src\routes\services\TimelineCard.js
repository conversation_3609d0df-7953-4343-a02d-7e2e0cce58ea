import React, { Component } from 'react';
import {
    Collapse,
    Form,
    Timeline,
    Input,
    Button,
    Rate,
    Col,
    Badge,
} from 'antd';
import CircularProgress from '../../components/CircularProgress';
import PagedApiListView from '../../components/wify-utils/crud/overview/PagedApiListView';
import {
    convertUTCToDisplayTime,
    getLabelFrmOptionsValue,
    getGeneralFileSection,
    convertDateFieldsToMoments,
} from '../../../src/util/helpers';
import AppModuleHeader from '../../components/AppModuleHeader';
import FormBuilder from 'antd-form-builder';
import moment from 'moment';
import {
    decodeFieldsMetaFrmJson,
    decodeFileSectionsFrmJson,
    decodeMicSectionsFrmJson,
    decodeCameraSectionsFrmJson,
} from '../../components/wify-utils/FieldCreator/helpers';
import http_utils from '../../util/http_utils';
import S3Uploader from '../../components/wify-utils/S3Uploader/S3Uploader';
import {
    getAllCustomFieldsFrStatuses,
    getAllPossibleStatusFormFields,
} from '../my-tasks/SingleStatusUpdates';
import AppCallStatus from './AppCallStatus';
import { PhoneOutlined } from '@ant-design/icons';
import MicInputV2 from '../../components/wify-utils/MicInput_v2';
import CameraInput from '../../components/wify-utils/CameraInput';
import { renderCommentWithLinks } from './helpers';
import { renderPartsConsumption } from '../../components/WIFY/PartsMangement/PartsConsumption';
import StarRatingCompact from '../../components/WIFY/WifyComponents/StarRatingCompact';

const { Panel } = Collapse;

const dataUrl = '/services/timeline';
const { TextArea } = Input;

export const getCommentstInfoMeta = () => {
    const meta = {
        columns: 4,
        formItemLayout: null,
        fields: [
            {
                key: 'comment',
                colSpan: 4,
                label: '',
                widget: 'textarea',
                placeholder: 'Enter comment and send',
                rules: [
                    {
                        required: true,
                        message: 'Please enter comment',
                    },
                ],
            },
        ],
    };
    return meta;
};

export const getAutoAuthoritiesRoleInfoMeta = () => {
    const meta = {
        columns: 4,
        formItemLayout: null,
        fields: [
            {
                key: 'authorities_role',
                label: 'Authorities Role',
            },
        ],
    };
    return meta;
};
export const getApprovalReqStatusChangeInfoMeta = () => {
    const meta = {
        columns: 4,
        formItemLayout: null,
        fields: [
            {
                key: 'to',
                label: 'To',
            },
            {
                key: 'cc',
                label: 'CC',
            },
        ],
    };
    return meta;
};

export const getServiceProviderMeta = () => {
    const meta = {
        columns: 4,
        formItemLayout: null,
        fields: [
            {
                key: 'new_prvdr',
                label: 'Service Provider',
            },
        ],
    };
    return meta;
};
class TimelineCard extends Component {
    constructor(props) {
        super(props);
        // console.log("this.props.formDataMeta",this.props.formDataMeta);
    }

    state = {
        activeFilters: {},
        drawerState: false,
        showItemEditor: false,
        isLoadingViewData: false,
        viewData: undefined,
        error: '',
        fileSections: [],
        micSections: [],
        cameraSections: [],
    };
    //this.props.srvcDetails.srvc_id  is srvc_type_id
    dataUrl =
        dataUrl +
        '/' +
        this.props.srvcDetails.srvc_id +
        '/' +
        this.props.srvcReqId;

    componentDidMount() {
        this.initViewData();
        this.initConfigData();
    }

    componentDidUpdate(prevProps, prevState) {
        if (prevProps.srvcConfigData != this.props.srvcConfigData) {
            this.initConfigData();
        }
    }

    initViewData() {
        this.setState({
            isLoadingViewData: false,
            // viewData: resp.data,
            viewData: {},
            error: '',
        });
    }

    getMetaIfFeedbackUpdate(form_data) {
        let meta = [];
        if (form_data?.feedback_data) {
            let feedback_meta = form_data?.feedback_data.form_meta;
            let jsonMetaFrDecoding = JSON.stringify({
                translatedFields: feedback_meta,
            });
            let decodedFeedbackmeta =
                decodeFieldsMetaFrmJson(jsonMetaFrDecoding);
            // console.log('decodedFeedbackmeta',decodedFeedbackmeta);
            meta = [...meta, ...decodedFeedbackmeta];
        }
        return meta;
    }

    getAllMetaForSubTaskTypeDetails(form_data) {
        let meta = [];
        if (form_data?.sbtsk_type_id) {
            let subtaskConfigData = this.props.subtaskConfigData;
            if (subtaskConfigData) {
                let subtTaskTypeDetails = subtaskConfigData.filter(
                    (item) => item.value == form_data.sbtsk_type_id
                )[0];

                if (subtTaskTypeDetails) {
                    subtTaskTypeDetails = {
                        subtTaskTypeDetails,
                    };
                    meta = [
                        ...getAllPossibleStatusFormFields(subtTaskTypeDetails),
                    ];
                }
            }
        }
        return meta;
    }

    initConfigData() {
        // debugger;
        let newFileSections = [getGeneralFileSection()];
        let custom_file_sections = this.getCustomFileSectionsFrmConfig();

        let newMicSections = [];
        let customMicSections = this.getCustomMicSectionsFrmConfig(); // service type custom mic sections

        let newCameraSections = [];
        let customCameraSections = this.getCustomCameraSectionsFrmConfig(); // service type custom camera sections

        //get all sub_task_type files
        let subtaskConfigData = this.props.subtaskConfigData;
        if (subtaskConfigData) {
            subtaskConfigData.map((singleSubtaskConfigData) => {
                let subtaskTypeConfigData = {
                    subtTaskTypeDetails: {
                        config_data: singleSubtaskConfigData.config_data,
                        statuses: singleSubtaskConfigData.statuses,
                        value: singleSubtaskConfigData.value,
                    },
                };
                let allPossibleCustomFileSectionsFrSubTaskType =
                    getAllCustomFieldsFrStatuses(subtaskTypeConfigData).files;
                if (allPossibleCustomFileSectionsFrSubTaskType.length > 0) {
                    newFileSections = [
                        ...newFileSections,
                        ...allPossibleCustomFileSectionsFrSubTaskType,
                    ];
                }
                let allPossibleCustomMicSectionsFrSubTaskType =
                    getAllCustomFieldsFrStatuses(
                        subtaskTypeConfigData
                    ).mic_files;
                if (allPossibleCustomMicSectionsFrSubTaskType.length > 0) {
                    newMicSections = [
                        ...newMicSections,
                        ...allPossibleCustomMicSectionsFrSubTaskType,
                    ];
                }
                let allPossibleCustomCameraSectionsFrSubTaskType =
                    getAllCustomFieldsFrStatuses(
                        subtaskTypeConfigData
                    ).camera_files;
                if (allPossibleCustomCameraSectionsFrSubTaskType.length > 0) {
                    newCameraSections = [
                        ...newCameraSections,
                        ...allPossibleCustomCameraSectionsFrSubTaskType,
                    ];
                }
            });
        }
        if (custom_file_sections && custom_file_sections.length > 0) {
            newFileSections = [...newFileSections, ...custom_file_sections];
        }
        if (customMicSections && customMicSections.length > 0) {
            newMicSections = [...newMicSections, ...customMicSections];
        }
        if (customCameraSections && customCameraSections.length > 0) {
            newCameraSections = [...newCameraSections, ...customCameraSections];
        }

        this.setState({
            fileSections: newFileSections,
            micSections: newMicSections,
            cameraSections: newCameraSections,
        });
    }

    getCustomFileSectionsFrmConfig() {
        return this.getCustomAttachmentSections(decodeFileSectionsFrmJson);
    }

    getCustomMicSectionsFrmConfig() {
        return this.getCustomAttachmentSections(decodeMicSectionsFrmJson);
    }

    getCustomCameraSectionsFrmConfig() {
        return this.getCustomAttachmentSections(decodeCameraSectionsFrmJson);
    }

    /**
     * This function retrieves and decodes custom attachment sections
     * from the service req and service provider configuration data.
     * It takes a decodeFunction as an argument, which should be a function
     * used to decode the JSON fields. If decodeFunction is not a function,
     * it logs an error message and returns an empty array.
     * Otherwise, it decodes the service custom fields and service provider
     * custom fields, and returns a combined array of decoded fields.
     *
     * @param {function} decodeFunction - A function used to decode JSON fields.
     * @returns {Array} An array of decoded custom attachment sections.
     */
    getCustomAttachmentSections(decodeFunction) {
        if (typeof decodeFunction !== 'function') {
            console.log('decodeFunction is not defined or not a function');
            return [];
        }
        const srvcCustFieldsJson =
            this.props.srvcConfigData?.srvc_cust_fields_json || [];
        const spSrvcCustFieldsJson =
            this.props.spConfigData?.sp_cust_fields_json || [];

        const decodedSrvcCustFields = decodeFunction(srvcCustFieldsJson) || [];
        const decodedSpSrvcCustFields =
            decodeFunction(spSrvcCustFieldsJson) || [];

        return [...decodedSrvcCustFields, ...decodedSpSrvcCustFields];
    }

    configFrPagedApiListView = {
        dataSourceApi: this.dataUrl,
        renderSingleItem: (item) => (
            <Timeline.Item color="green" key={item.id} className="">
                <h4 className="gx-m-0">
                    {item.update_type == 'CALL_STATUS' && (
                        <PhoneOutlined className="gx-mr-2" />
                    )}
                    {item.title}
                </h4>
                <span className="gx-fs-sm gx-text-grey gx-ml-3">
                    {convertUTCToDisplayTime(item.start_time)} -{' '}
                    {item.c_by == 'Customer'
                        ? this.props.custName + ' (' + item.c_by + ')'
                        : item.c_by}
                </span>

                {item.update_type == 'CALL_STATUS' ? (
                    <AppCallStatus txn_log_item={item} />
                ) : (
                    this.props.formDataMeta &&
                    this.getViewModeFormBuilder(
                        [
                            ...this.props.formDataMeta,
                            ...this.getMetaIfFeedbackUpdate(item.form_data),
                            ...this.getAllMetaForSubTaskTypeDetails(
                                item.form_data
                            ),
                        ],
                        item.form_data?.feedback_data?.form_data ||
                            item.form_data
                    )
                )}
            </Timeline.Item>
        ),
    };

    replaceEmptyArrayValuesForUUIDKeys(form_data) {
        const uuidRegex =
            /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;

        for (const key in form_data) {
            if (uuidRegex.test(key)) {
                if (
                    Array.isArray(form_data[key]) &&
                    form_data[key].length === 0
                ) {
                    form_data[key] = ''; // Replace only empty arrays for UUID keys
                }
            }
        }
        return form_data;
    }
    getPrvdrByKey(currentSrvcPrvdrKey) {
        return this.props.allSrvcPrvdrs.filter(
            (singlePrvdr) => singlePrvdr.value == currentSrvcPrvdrKey
        )[0];
    }

    getViewModeFormBuilder(formMeta, form_data) {
        // return "Hi";
        form_data = this.replaceEmptyArrayValuesForUUIDKeys(form_data);
        let formMetaFrDisplay = [];
        formMeta.forEach((singleFieldMeta) => {
            let key = singleFieldMeta.key;
            if (form_data[key]) {
                delete singleFieldMeta['colSpan'];
                if (singleFieldMeta.widget == 'date-picker') {
                    singleFieldMeta.renderView = (value) => (
                        <div>{moment.utc(value).format('MMM Do YYYY')}</div>
                    );
                } else if (singleFieldMeta.widget == 'select') {
                    singleFieldMeta.renderView = (value) => (
                        <span>
                            {getLabelFrmOptionsValue(
                                singleFieldMeta.options,
                                value
                            )}
                        </span>
                    );
                } else if (singleFieldMeta.widget?.displayName == 'Rate') {
                    singleFieldMeta.renderView = (value) => (
                        <StarRatingCompact
                            rated={value}
                            maxRating={this.props?.feedback_data['maxRating']}
                        />
                    );
                } else if (singleFieldMeta.widget == 'radio-group') {
                    singleFieldMeta.renderView = (value) => (
                        <span>
                            {getLabelFrmOptionsValue(
                                singleFieldMeta.options,
                                value
                            )}
                        </span>
                    );
                } else if (singleFieldMeta.widget == 'checkbox-group') {
                    singleFieldMeta.renderView = (value) => (
                        <span>
                            {getLabelFrmOptionsValue(
                                singleFieldMeta.options,
                                value
                            )}
                        </span>
                    );
                } else if (singleFieldMeta.key == 'comment') {
                    singleFieldMeta.renderView = (value) => {
                        return renderCommentWithLinks(value, true);
                    };
                } else if (singleFieldMeta.key == 'new_prvdr') {
                    singleFieldMeta.renderView = (value) => (
                        <span>{this.getPrvdrByKey(value)?.full_name}</span>
                    );
                } else if (singleFieldMeta.key == 'cust_full_name' && singleFieldMeta.render) {
                    singleFieldMeta.renderView = (value) => (
                        <span>{value}</span>
                    );
                    delete singleFieldMeta.render;
                }
                // key exists in form_data so this field needs to be displayed
                formMetaFrDisplay.push(singleFieldMeta);
            }
        });

        const fileAttachments =
            form_data.attachments != undefined &&
            Object.keys(form_data?.attachments).length != 0;
        const hasMicRecordings =
            form_data.mic_files != undefined &&
            Object.keys(form_data?.mic_files).length != 0;
        const hasCameraRecordings =
            form_data.camera_files != undefined &&
            Object.keys(form_data?.camera_files).length != 0;
        return (
            <div className={`${formMetaFrDisplay.length > 0 ? '' : 'gx-py-2'}`}>
                {(formMetaFrDisplay.length > 0 ||
                    fileAttachments ||
                    hasMicRecordings ||
                    hasCameraRecordings) && (
                    <Collapse ghost>
                        <Panel header="View data" key="1">
                            <Form layout="vertical">
                                <FormBuilder
                                    meta={{
                                        formItemLayout: null,
                                        columns: 2,
                                        fields: formMetaFrDisplay,
                                    }}
                                    initialValues={form_data}
                                    viewMode
                                />
                                {hasMicRecordings &&
                                    this.state.micSections?.map(
                                        (singleMicSection, index) =>
                                            form_data?.mic_files[
                                                singleMicSection.key
                                            ]?.length > 0 && (
                                                <Col
                                                    xs={24}
                                                    md={24}
                                                    className="gx-pl-0"
                                                    key={singleMicSection.key}
                                                >
                                                    {singleMicSection.title !=
                                                        '' && (
                                                        <h3 className="gx-mt-3">
                                                            {
                                                                singleMicSection.title
                                                            }
                                                            <hr className="gx-bg-dark"></hr>
                                                        </h3>
                                                    )}
                                                    <MicInputV2
                                                        readOnly
                                                        authToken={http_utils.getAuthToken()}
                                                        prefixDomain={http_utils.getCDNDomain()}
                                                        initialFiles={
                                                            form_data
                                                                ?.mic_files?.[
                                                                singleMicSection
                                                                    .key
                                                            ]
                                                        }
                                                    />
                                                </Col>
                                            )
                                    )}
                                {hasCameraRecordings &&
                                    this.state.cameraSections?.map(
                                        (singleCameraSection, index) =>
                                            form_data?.camera_files[
                                                singleCameraSection.key
                                            ]?.length > 0 && (
                                                <Col
                                                    xs={24}
                                                    md={24}
                                                    className="gx-pl-0"
                                                    key={
                                                        singleCameraSection.key
                                                    }
                                                >
                                                    {singleCameraSection.title !=
                                                        '' && (
                                                        <h3 className="gx-mt-3">
                                                            {
                                                                singleCameraSection.title
                                                            }
                                                            <hr className="gx-bg-dark"></hr>
                                                        </h3>
                                                    )}
                                                    <CameraInput
                                                        readOnly
                                                        authToken={http_utils.getAuthToken()}
                                                        prefixDomain={http_utils.getCDNDomain()}
                                                        initialFiles={
                                                            form_data
                                                                ?.camera_files?.[
                                                                singleCameraSection
                                                                    .key
                                                            ]
                                                        }
                                                    />
                                                </Col>
                                            )
                                    )}
                                {fileAttachments &&
                                    this.state.fileSections?.map(
                                        (singleFileSection, index) =>
                                            form_data?.attachments[
                                                singleFileSection.key
                                            ]?.length > 0 && (
                                                <Col
                                                    xs={24}
                                                    md={24}
                                                    className="gx-pl-0"
                                                    key={singleFileSection.key}
                                                >
                                                    {singleFileSection.title !=
                                                        '' && (
                                                        <h3 className="gx-mt-3">
                                                            {
                                                                singleFileSection.title
                                                            }
                                                            <hr className="gx-bg-dark"></hr>
                                                        </h3>
                                                    )}
                                                    <S3Uploader
                                                        // className="gx-w-50"
                                                        // demoMode
                                                        maxColSpan={4}
                                                        readOnly
                                                        authToken={http_utils.getAuthToken()}
                                                        prefixDomain={http_utils.getCDNDomain()}
                                                        initialFiles={
                                                            form_data
                                                                ?.attachments?.[
                                                                singleFileSection
                                                                    .key
                                                            ]
                                                        }
                                                        showDeleteBtn={false}
                                                        customPreviewHeight="100%"
                                                        customFileIconMaxWidth="40px"
                                                        compConfig={{
                                                            name: 'service-request-timeline',
                                                        }}
                                                    />
                                                </Col>
                                            )
                                    )}
                                {renderPartsConsumption(
                                    form_data.sbtsk_parts_consumption
                                )}
                            </Form>
                        </Panel>
                    </Collapse>
                )}
            </div>
        );
    }

    resetFilter = () => {
        this.setState({
            activeFilters: {},
        });
    };

    handleFilterChange = (newFilterObject) => {
        this.setState({
            activeFilters: {
                ...this.state.activeFilters,
                ...newFilterObject,
            },
        });
    };

    handleSearchChange = (query) => {
        // console.log("Rxd search:", query);
        this.setState({
            searchFilter: query,
        });
    };

    getFilters() {
        return {};
    }

    notifyDataSetChanged(entry_id) {
        // console.log("Refresh list called in parent",this);
        if (this.state.editorItem == undefined) {
            // console.log("Editor item was undefined, refreshing list");
            // new entry was created, we will have to clear filters
            // refresh list
            this.resetFilter();
        } else {
            // Refresh along with existing filters
            this.setState({
                activeFilters: { ...this.state.activeFilters },
            });
        }
    }

    submitForm = (data) => {
        if (data.comment?.trim() != '') {
            this.props.onDataSubmitComment(data);
        }
    };

    render() {
        const {
            activeFilters,
            drawerState,
            showEditor,
            editorItem,
            searchFilter,
            isLoadingViewData,
            showItemEditor,
            error,
            viewData,
        } = this.state;
        return (
            <div className="gx-mb-1 gx-pl-3">
                {isLoadingViewData ? (
                    <div className="gx-loader-view gx-loader-position">
                        <CircularProgress />
                    </div>
                ) : viewData == undefined ? (
                    <p className="gx-text-red">{error}</p>
                ) : (
                    <div className="gx-module-box-content ">
                        <Form
                            className="gx-w-100"
                            layout="vertical"
                            ref={this.formRef}
                            onFinish={(data) => {
                                this.submitForm(data);
                            }}
                        >
                            <div className="gx-flex-row gx-align-items-center">
                                <div className="gx-col">
                                    <FormBuilder
                                        key="comment"
                                        meta={getCommentstInfoMeta()}
                                        form={this.formRef}
                                    />
                                </div>
                                <div>
                                    <Button type="dashed" htmlType="submit">
                                        Send
                                    </Button>
                                </div>
                            </div>
                        </Form>
                        <AppModuleHeader
                            placeholder="Search by action, user.."
                            currValue={this.state.searchFilter}
                            onChange={this.handleSearchChange}
                        />
                        <Timeline>
                            <PagedApiListView
                                {...this.configFrPagedApiListView}
                                filterObject={activeFilters}
                                searchQuery={searchFilter}
                                noTotal
                            />
                        </Timeline>
                    </div>
                )}
            </div>
        );
    }
}

export default TimelineCard;
