import moment from 'moment';

async function ruleValidator(
    formMeta = [],
    formData = {},
    isBulkAssignComp = false
) {
    const errors = [];
    let forceToChkValidation = false;
    // console.log('yeti formMeta', formMeta, 'formData ', formData);

    for (const data of [formData]) {
        const obj = {};
        const dataErrors = [];

        for (const field of formMeta) {
            const {
                key,
                label,
                required,
                rules = [],
                widget,
                options = [],
                widgetProps,
            } = field;
            const value = data[key];
            const fieldErrors = [];

            const requiredError = validateRequired(
                value,
                label,
                required,
                isBulkAssignComp
            );
            if (requiredError) {
                fieldErrors.push(requiredError);
            }
            if (
                widget == 'date-picker' &&
                formData.hasOwnProperty(key) &&
                (formData[key] == '' || formData[key] == null)
            ) {
                forceToChkValidation = true;
            } else {
                forceToChkValidation = false;
            }
            if (value || forceToChkValidation || rules.length > 0) {
                const typeError = validateType(value, label, rules);
                if (typeError) {
                    fieldErrors.push(typeError);
                }

                const ruleErrors = await validateRules(
                    value,
                    label,
                    rules,
                    formData,
                    required
                );
                if (ruleErrors) {
                    fieldErrors.push(...ruleErrors);
                }
                if (value) {
                    const widgetError = validateWidget(value, label, widget);
                    if (widgetError) {
                        fieldErrors.push(widgetError);
                    }
                }

                if (options.length > 0) {
                    // this means the the value for this param must be within the options array
                    let optionMismatchError;
                    if (!required && (!value || value.length == 0)) {
                        //do not validate select fields where required is false and no option selected
                        optionMismatchError = undefined;
                    } else {
                        optionMismatchError = validateOptionWithValue(
                            value,
                            label,
                            options,
                            widgetProps
                        );
                    }
                    if (optionMismatchError) {
                        fieldErrors.push(optionMismatchError);
                    }
                }
            }

            if (fieldErrors.length > 0) {
                obj[key] = value;
                dataErrors.push(...fieldErrors);
            }
        }

        if (dataErrors.length > 0) {
            obj.errors = dataErrors;
            errors.push(obj);
        }
    }
    // console.log('yeti errors', errors);
    return errors;
}
function validateRequired(value, label, required, isBulkAssignComp) {
    if (required && !value) {
        if (isBulkAssignComp && label == 'Subtask type') {
            return `${label} is required or invalid or you don't have access to create this subtask.`;
        }
        return `${label} is required.`;
    }
    return null;
}
//validateType: Validates if a value matches a specific data type and returns an error message if the value is not of that type.
function validateType(value, label, rules) {
    let type;
    for (const rule of rules) {
        if (rule.type) {
            type = rule.type;
            break;
        }
    }

    if (type && value !== undefined && value !== null) {
        switch (type) {
            case 'string':
                if (typeof value !== 'string') {
                    return `${label} must be a string.`;
                }
                break;
            case 'number':
                if (typeof value !== 'number') {
                    return `${label} must be a number.`;
                }
                break;
            case 'boolean':
                if (typeof value !== 'boolean') {
                    return `${label} must be a boolean.`;
                }
                break;
            case 'integer':
                if (!Number.isInteger(value)) {
                    return `${label} must be an integer.`;
                }
                break;
            case 'float':
                if (
                    typeof value !== 'number' ||
                    Number.isNaN(value) ||
                    !Number.isFinite(value)
                ) {
                    return `${label} must be a finite number.`;
                }
                break;
            // case 'date':
            //     if (!(value instanceof Date) || isNaN(value.getTime())) {
            //         return `${label} must be a valid date.`;
            //     }
            //     break;
            case 'email':
                if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
                    return `${label} must be a valid email address.`;
                }
                break;
            default:
                throw new Error(`Invalid type: ${type}`);
        }
    }

    return null;
}

//validateRules: Validates if a value matches a specific pattern or meets a certain length requirement and returns an error message if the value does not meet the criteria.
async function validateRules(value, label, rules, formData = {}, fieldRequired = false) {
    const isEmpty = !value;

    console.log(`AntdRuleValidator :: validateRules :: fieldRequired ${fieldRequired}`)
    // Early return: if field is not required and value is empty, skip all validation rules
    if (!fieldRequired && isEmpty) {
        console.log(`AntdRuleValidator :: validateRules :: returning ${label}`)
        return null;
    }

    const errors = [];

    for (const rule of rules) {
        const { message, pattern, min, max, validator, required } = rule;
        // console.log('yeti rule--->', rule);

        // Pattern Validation
        if (
            pattern &&
            typeof pattern === 'string' &&
            !new RegExp(pattern).test(value)
        ) {
            errors.push(message || `${label} is invalid.`);
        }

        // Min Length Validation
        if (min !== undefined && value?.length < min) {
            errors.push(
                message || `${label} must be at least ${min} characters.`
            );
        }

        // Max Length Validation
        if (max !== undefined && value?.length > max) {
            errors.push(
                message || `${label} must be at most ${max} characters.`
            );
        }

        // Required Field Validation
        if (required && (value === undefined || value === '')) {
            errors.push(message || `${label} is required.`);
        }

        // Validator Handling (Asynchronous)
        if (validator) {
            try {
                // Call the async/sync validator
                const validationResult = await validator(
                    { getFieldValue: (key) => formData[key] },
                    value
                );
                // If it resolves with an error message, add it to the errors
                if (validationResult) {
                    errors.push(validationResult);
                }
            } catch (error) {
                // If validator throws an error, add the error message
                errors.push(
                    error.message || error || `${label} validation failed.`
                );
            }
        }
    }
    return errors.length > 0 ? errors : null;
}

function validateOptionWithValue(value, label, options, widgetProps) {
    const labels = options.map((option) => option.label);

    if (Array.isArray(options) && options.length > 0) {
        let matched = false;

        // Check if the widget is in multi-select mode
        if (widgetProps?.mode === 'multiple') {
            // Ensure all selected values are valid options
            if (Array.isArray(value)) {
                for (const selectedValue of value) {
                    const isValid = options.some((option) => {
                        return typeof option === 'object'
                            ? option.value === selectedValue
                            : option === selectedValue;
                    });

                    if (!isValid) {
                        return `Please enter correct ${label}`;
                    }
                }
            } else {
                // If value is not an array, it's invalid for multi-select
                return `Please select at least one ${label}`;
            }
        } else {
            // Handle single select scenario
            matched = options.some((singleOption) => {
                return typeof singleOption === 'object'
                    ? singleOption.value === value
                    : singleOption === value;
            });

            if (!matched) {
                return `Please enter correct ${label}`;
            }
        }
    }

    return undefined;
}
function validateWidget(value, label, widget) {
    switch (widget) {
        case 'number':
            if (typeof value !== 'number') {
                return `${label} must be a number.`;
            }
            break;
        // case 'date-picker':
        //     if (
        //         value &&
        //         !/^\d{4}-(0[1-9]|1[0-2])-([0-2]\d|3[01])$/.test(value)
        //     ) {
        //         return `${label} must be in YYYY-MM-DD Format.`;
        //     }
        //     break;
    }
    return undefined;
}

export default ruleValidator;
