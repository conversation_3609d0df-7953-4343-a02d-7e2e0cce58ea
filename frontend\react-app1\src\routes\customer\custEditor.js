import {
    Modal,
    Form,
    Input,
    Button,
    Select,
    Upload,
    Row,
    Col,
    Collapse,
    Tabs,
} from 'antd';
import React, { Component } from 'react';
import CircularProgress from '../../components/CircularProgress';
import http_utils from '../../util/http_utils';
import FormBuilder from 'antd-form-builder';
import RemoteSourceSelect from '../../components/wify-utils/RemoteSourceSelect';
import { UploadOutlined } from '@ant-design/icons';
import BulkUploader from '../../components/wify-utils/BulkUploader';
import CustomerHistory from '../../components/WIFY/CustomerHistory';
import { getAddressFieldsMeta } from '../../util/CustomerHelpers';
import checkFeatureAccess from '../../util/FeatureAccess';
import ConfigHelpers from '../../util/ConfigHelpers';

const { Option } = Select;
const { TabPane } = Tabs;
const protoUrl = '/cust/proto';
const submitUrl = '/cust';

class CustEditor extends Component {
    constructor(props) {
        super(props);
        this.formRef = React.createRef();
    }

    state = {
        render_helper: false,
        visible: false,
        isFormSubmitting: false,
        viewData: undefined,
        isLoadingViewData: false,
        error: '',
        fileList: [],
        editMode: this.props.editMode,
        editModeForceRefreshDone: false,
        customerFieldConfigEnabled: false,
    };

    componentDidMount() {
        this.initViewData();
        this.checkCustomerFieldConfig();
    }

    async checkCustomerFieldConfig() {
        try {
            const hasAccess = await checkFeatureAccess('TMS250707644389');
            console.log({hasAccess});
            this.setState({ customerFieldConfigEnabled: hasAccess });
        } catch (error) {
            console.error('Error checking customer field config feature:', error);
            this.setState({ customerFieldConfigEnabled: false });
        }
    }

    getRequiredFieldsConfig() {
        const defaultConfig = { cust_full_name: true, cust_code: true, cust_email: true, cust_mobile: true };
        const featureEnabledConfig = { cust_full_name: true, cust_code: false, cust_email: false, cust_mobile: false };
        return this.state.customerFieldConfigEnabled ? featureEnabledConfig : defaultConfig;
    }

    handleFileChanged(data) {
        this.submitForm(data);
    }

    initViewData() {
        if (
            (this.state.editMode && this.state.visible) ||
            (!this.state.editMode &&
                this.state.viewData == undefined &&
                !this.state.isLoadingViewData)
        ) {
            this.setState({
                isLoadingViewData: true,
            });
            var params = {};
            const onComplete = (resp) => {
                // console.log("resp",resp);
                this.setState({
                    isLoadingViewData: false,
                    viewData: resp.data,
                    error: '',
                });
            };
            const onError = (error) => {
                // console.log(error.response.status);
                this.setState({
                    isLoadingViewData: false,
                    error: http_utils.decodeErrorToMessage(error),
                });
            };

            var url = !this.state.editMode
                ? protoUrl
                : protoUrl + '/' + this.props.editorItem.cust_id;

            http_utils.performGetCall(url, params, onComplete, onError);
        }
    }

    componentDidUpdate(prevProps, prevState) {
        if (
            prevProps.editorItem != this.props.editorItem ||
            prevProps.showEditor != this.props.showEditor
        ) {
            this.setState(
                {
                    render_helper: !this.state.render_helper,
                    visible: this.props.showEditor,
                },
                function () {
                    if (this.props.showEditor && this.state.editMode) {
                        this.initViewData();
                        this.setState({
                            editModeForceRefreshDone: false,
                        });
                    }
                }
            );
        }
    }
    handleOk = () => {
        this.setState({
            ModalText: 'The modal will be closed after two seconds',
            isFormSubmitting: true,
        });
        setTimeout(() => {
            this.setState({
                visible: false,
                isFormSubmitting: false,
            });
            this.updateClosureToParent();
        }, 2000);
    };

    updateClosureToParent() {
        if (this.props.onClose != undefined) {
            this.props.onClose();
        }
    }
    tellParentToRefreshList(entry_id) {
        console.log('Trying to to tell parent to refresh list');
        if (this.props.onDataModified != undefined) {
            this.props.onDataModified(entry_id);
        }
    }

    handleCancel = () => {
        this.setState({
            visible: false,
        });
        this.updateClosureToParent();
    };

    submitForm = (data) => {
        this.setState({
            isFormSubmitting: true,
        });
        var params = data;
        // console.log("params",params);

        const onComplete = (resp) => {
            this.setState({
                isFormSubmitting: false,
                error: '',
                visible: false,
            });
            this.tellParentToRefreshList(resp.entry_id);
            this.updateClosureToParent();
        };
        const onError = (error) => {
            console.log('Got error', error);
            // compare statuses here
            this.setState({
                isFormSubmitting: false,
                error: error.response.data,
            });
        };

        if (this.state.editMode) {
            http_utils.performPutCall(
                submitUrl + '/' + this.props.editorItem.cust_id,
                params,
                onComplete,
                onError
            );
        } else {
            http_utils.performPostCall(submitUrl, params, onComplete, onError);
        }
    };

    getMeta = () => {
        if (this.state.editMode && !this.state.editModeForceRefreshDone) {
            // refreshing state to get form ref
            this.setState({ editModeForceRefreshDone: true });
            return;
        }

        const countryCode = this.state.viewData?.country_code;
        const MobileDigit = this.state.viewData?.mobile_digit;
        const requiredFields = this.getRequiredFieldsConfig();

        const meta = {
            columns: 2,
            formItemLayout: null,
            fields: [
                {
                    key: 'cust_full_name',
                    label: 'Full Name',
                    placeholder: 'Eg: john',
                    required: requiredFields.cust_full_name,
                    rules: [
                        {
                            max: 100,
                            message:
                                'customer full name must be max 100 characters.',
                        },
                    ],
                },
                {
                    key: 'cust_code',
                    label: 'Code',
                    placeholder: 'Eg: 001',
                    required: requiredFields.cust_code,
                    rules: [
                        {
                            max: 200,
                            message:
                                'customer code must be max 100 characters.',
                        },
                    ],
                },
                {
                    key: 'cust_email',
                    label: 'Email',
                    placeholder: 'Eg: <EMAIL>',
                    required: requiredFields.cust_email,
                    rules: [
                        {
                            type: 'email',
                        },
                    ],
                },
                {
                    key: 'cust_mobile',
                    label: `Mobile Number (${countryCode})`,
                    placeholder: 'Eg: 9876543210',
                    required: requiredFields.cust_mobile,
                    rules: [
                        {
                            // required: true,
                            pattern: new RegExp('^[0-9]*$'),
                            message: 'Please enter your right mobile number!',
                        },
                        {
                            min: MobileDigit,
                            message: `Mobile(${countryCode}) must be ${MobileDigit} characters.`,
                        },
                        {
                            max: MobileDigit,
                            message: `Mobile(${countryCode}) must be ${MobileDigit} characters.`,
                        },
                    ],
                },
                ...getAddressFieldsMeta(
                    this.formRef,
                    () => {
                        this.forceUpdate();
                    },
                    false,
                    this.state.viewData
                ),
            ],
        };
        return meta;
    };

    render() {
        const { editorItem } = this.props;
        const {
            isFormSubmitting,
            visible,
            isLoadingViewData,
            error,
            viewData,
        } = this.state;
        var editorTitle = editorItem?.full_name;
        var editMode = true;
        if (editorTitle == undefined) {
            editorTitle = 'Create new customer';
            editMode = false;
        }
        const cust_mobile = this.state.viewData?.form_data?.cust_mobile;
        const cust_id = this.state.viewData?.form_data?.cust_id;
        return visible ? (
            <Modal
                title={`${editorTitle}`}
                visible={visible}
                onOk={this.handleOk}
                confirmLoading={isFormSubmitting}
                onCancel={this.handleCancel}
                width={1000}
                style={{
                    marginTop: '-70px',
                }}
                bodyStyle={{
                    minHeight: '85vh',
                    padding: '20px',
                    paddingTop: '0px',
                }}
                footer={null}
            >
                {isLoadingViewData ? (
                    <div className="gx-loader-view gx-loader-position">
                        <CircularProgress />
                    </div>
                ) : viewData == undefined ? (
                    <p className="gx-text-red">{error}</p>
                ) : (
                    <Row>
                        {!editMode && (
                            <Col xs={24} className="gx-my-1">
                                <Collapse>
                                    <Collapse.Panel
                                        header={
                                            <span className="gx-text-primary">
                                                <UploadOutlined className="gx-mr-2" />
                                                Click here for Bulk creation
                                            </span>
                                        }
                                    >
                                        <div>
                                            <BulkUploader
                                                onDataModified={(entry_ids) =>
                                                    this.tellParentToRefreshList(
                                                        0
                                                    )
                                                }
                                                submitUrl={submitUrl}
                                                dataProto={
                                                    this.getMeta().fields
                                                }
                                                orgSettingsData={
                                                    this.state.viewData
                                                }
                                            />
                                        </div>
                                    </Collapse.Panel>
                                </Collapse>
                            </Col>
                        )}
                        <Col xs={24}>
                            <Tabs defaultActiveKey="1">
                                <TabPane
                                    key="1"
                                    tab={
                                        this.state.editMode
                                            ? 'Details'
                                            : 'Customer Details'
                                    }
                                    className="gx-ml-1"
                                >
                                    <Form
                                        className="ant-col gx-my-1 ant-col-xs-24 gx-mt-3"
                                        layout="vertical"
                                        ref={this.formRef}
                                        onFinish={(data) => {
                                            this.submitForm(data);
                                        }}
                                        initialValues={
                                            this.state.editMode
                                                ? this.state.viewData?.form_data
                                                : {}
                                        }
                                    >
                                        <FormBuilder
                                            meta={this.getMeta()}
                                            form={this.formRef}
                                        />

                                        <Form.Item>
                                            <Button
                                                type="primary"
                                                htmlType="submit"
                                                disabled={isFormSubmitting}
                                            >
                                                {editMode ? 'Save' : 'Submit'}
                                            </Button>
                                        </Form.Item>
                                        {isFormSubmitting ? (
                                            <div className="gx-loader-view gx-loader-position">
                                                <CircularProgress />
                                            </div>
                                        ) : null}
                                        {error ? (
                                            <p className="gx-text-red">
                                                {error}
                                            </p>
                                        ) : null}
                                    </Form>
                                </TabPane>
                                {this.state.editMode && (
                                    <TabPane key="2" tab="History">
                                        <CustomerHistory
                                            cust_id={ this.state.viewData?.enable_customer_profile_creation_based_on_name ? cust_id : null}
                                            cust_mobile={cust_mobile}
                                        />
                                    </TabPane>
                                )}
                            </Tabs>
                        </Col>
                    </Row>
                )}
            </Modal>
        ) : (
            <></>
        );
    }
}

export default CustEditor;
