var sampleOperationResp = require('../api_models/utils/operationResp');
var HttpStatus = require('http-status-codes');
var db_resp = require('../api_models/utils/db_resp');
const pagination_filters_utils = require('../api_models/utils/pagination_filters_utils');
const users_model = require('./users_model');

class searcher {
    searchers = {
        getCustomers: (query) => {
            //added new parameter
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            // query['cache_code'] = `service_${query.srvc_type_id}_labels`;
            var form_data = JSON.stringify(query);
            // console.log("db fn param",form_data);
            this.calledDbFn = 'tms_search_customers';
            return this.db.tms_search_customers(form_data);
        },
        getUsers: (query) => {
            //added new parameter
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            // query['cache_code'] = `service_${query.srvc_type_id}_labels`;
            var form_data = JSON.stringify(query);
            // console.log('db fn param', form_data);
            this.calledDbFn = 'tms_search_users';
            return this.db.tms_search_users(form_data);
        },
        getAllOrgUsers: (query) => {
            //added new parameter
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            // query['cache_code'] = `service_${query.srvc_type_id}_labels`;
            var form_data = JSON.stringify(query);
            this.calledDbFn = 'tms_get_matching_users';
            return this.db.tms_get_matching_users(form_data);
        },
        getCities: (query) => {
            //added new parameter
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['column_name'] = 'dist';
            // query['cache_code'] = `service_${query.srvc_type_id}_labels`;
            var form_data = JSON.stringify(query);
            // console.log("db fn param",form_data);
            this.calledDbFn = 'tms_search_location';
            return this.db.tms_search_location(form_data);
        },
        getState: (query) => {
            //added new parameter
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['column_name'] = 'state';
            // query['cache_code'] = `service_${query.srvc_type_id}_labels`;
            var form_data = JSON.stringify(query);
            // console.log("db fn param",form_data);
            this.calledDbFn = 'tms_search_location';
            return this.db.tms_search_location(form_data);
        },
        getPincode: (query) => {
            //added new parameter
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['column_name'] = 'pincode';
            // query['cache_code'] = `service_${query.srvc_type_id}_labels`;
            var form_data = JSON.stringify(query);
            // console.log("db fn param",form_data);
            this.calledDbFn = 'tms_search_location';
            return this.db.tms_search_location(form_data);
        },
        getCitiesByState: (query) => {
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['column_name'] = 'dist';
            // query['cache_code'] = `service_${query.srvc_type_id}_labels`;
            var form_data = JSON.stringify(query);
            // console.log("db fn param",form_data);
            this.calledDbFn = 'tms_get_matching_cities_by_state';
            return this.db.tms_get_matching_cities_by_state(form_data);
        },
        getPincodesByCity: (query) => {
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['column_name'] = 'pincode';
            // query['cache_code'] = `service_${query.srvc_type_id}_labels`;
            var form_data = JSON.stringify(query);
            // console.log('db fn param', form_data);
            this.calledDbFn = 'tms_get_matching_pincodes_by_city';
            return this.db.tms_get_matching_pincodes_by_city(form_data);
        },
        getServiceRequestLabels: (query) => {
            // query['srvc_type_id'] = query.srvc_type_id;
            // console.log("Trying to get overview data for ",this.srvcTypeId );
            //added new parameter
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['cache_code'] = `service_${query.srvc_type_id}_labels`;
            var form_data = JSON.stringify(query);
            // console.log("db fn param",form_data);
            this.calledDbFn = 'tms_search_cache';
            return this.db.tms_search_cache(form_data);
        },
        getSearchStockSerialNums: (query) => {
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            var form_data = JSON.stringify(query);
            this.calledDbFn = 'tms_search_stock_serial_nums';
            return this.db.tms_search_stock_serial_nums(form_data);
        },
        getSearchProductSkus: (query) => {
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            var form_data = JSON.stringify(query);
            this.calledDbFn = 'tms_search_product_skus';
            return this.db.tms_search_product_skus(form_data);
        },
        getSearchOldSiteRefResults: (query) => {
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            const searchQuery = query.query;
            var form_data = JSON.stringify(query);
            this.calledDbFn = 'tms_get_old_site_ref_search_results_fr_mkw';
            return this.db.tms_get_old_site_ref_search_results_fr_mkw(
                form_data,
                searchQuery
            );
        },
        getServiceHubs: (query) => {
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            const searchQuery = query.query;
            var form_data = JSON.stringify(query);
            this.calledDbFn = 'tms_get_srvc_hubs_by_search';
            return this.db.tms_get_srvc_hubs_by_search(form_data, searchQuery);
        },
    };

    getSearchResults(query) {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            if (query.query?.trim() == '') {
                resolve(
                    new sampleOperationResp(
                        false,
                        'Parameter failure',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            var fn = query.fn;
            this.searchers[fn](query).then(
                (res) => {
                    var dbResp = new db_resp(res[0][this.calledDbFn]);

                    if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
            return;
        });
    }

    fatalDbError(resolve, error) {
        // This is db level error need to be captured
        // mandatorily include this
        resolve(
            new sampleOperationResp(
                false,
                error,
                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
            )
        );
    }

    set ip_addr(ip_address) {
        this.ip_address = ip_address;
    }
    set user_agent(user_agent_) {
        this.user_agent_ = user_agent_;
    }

    set database(db) {
        this.db = db;
    }

    get database() {
        return this.db;
    }

    set user_context(userContext) {
        this.userContext = userContext;
    }

    get user_context() {
        return this.userContext;
    }

    getInstance(model) {
        const clonedInstance = new searcher();
        Object.assign(clonedInstance, model);
        return clonedInstance;
    }
}

module.exports = new searcher();
