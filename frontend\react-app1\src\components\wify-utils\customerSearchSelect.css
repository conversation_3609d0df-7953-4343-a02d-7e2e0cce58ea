/* Customer Search Select Component Styles */

.wy-customer-search-container {
    position: relative;
    width: 100%;
}

.wy-customer-search-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #ebebeb;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    max-height: 350px;
    overflow-y: auto;
}

.wy-customer-search-loading {
    padding: 16px;
    text-align: center;
}

.wy-customer-search-option {
    padding: 10px;
    cursor: pointer;
    display: flex;
    gap: 12px;
    border-bottom: 1px solid #f0f0f0;
    background: #fff;
    border-radius: 5px;
    margin: 5px;
    transition:all 0.3s ease-in-out;
}

.wy-customer-search-option:hover {
    background-color: #e9e9e9;
   box-shadow: 0 1px 4px -2px #000;
   transition:all 0.3s ease-in-out;
}

.wy-customer-avatar {
    background: #ddf4ff;
    border-radius: 5px;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.wy-customer-avatar-icon {
    color: #1890ff;
    font-size: 12px;
}

.wy-customer-details {
    flex: 1;
}

.wy-customer-header {
    display: flex;
    justify-content: space-between;
    font-weight: 400;
    margin-bottom: 2px;
}

.wy-customer-mobile {
    color: #595959;
}

.wy-customer-email {
    color: #333333;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.wy-customer-address {
    display: flex;
    color: #000000;
    gap: 4px;
    font-weight: 400;
}

.wy-customer-address-text {
    width: 100%;
}

.wy-customer-search-message {
    padding: 12px;
    color: #999;
    text-align: center;
}

.wy-customer-search-no-results {
    padding: 12px;
    color: #999;
    text-align: center;
}

.wy-customer-name-ellipsis {
    color: #000;
}

.wy-customer-flex-wrapper {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    gap: 5px;
}

.wy-customer-missing-details {
    font-style: italic;
    color: #a3a3a3;
}
