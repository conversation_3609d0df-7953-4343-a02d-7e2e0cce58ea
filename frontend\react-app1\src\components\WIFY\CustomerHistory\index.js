import React, { Component } from 'react';
import { Collapse, Tabs, Card } from 'antd';
import CircularProgress from '../../../components/CircularProgress';
import RecentRequest from '../../../components/WIFY/dashboard/CustUserDashboard/RecentRequest';
import http_utils from '../../../util/http_utils';
import { convertUTCToDisplayTime } from '../../../util/helpers';
import { HistoryOutlined } from '@ant-design/icons';
import { NoData } from '../../../util/helpers';
import RecentReqFrCustHistory from '../CustomerHistory/RecentReqFrCustHistory';
import checkFeatureAccess from '../../../util/FeatureAccess';

const { TabPane } = Tabs;
const { Panel } = Collapse;
const dataUrl = '/services/cust_history';

class CustomerHistory extends Component {
    constructor(props) {
        super(props);
    }

    state = {
        isLoadingViewData: false,
        viewData: undefined,
        error: '',
        render_helper: false,
        isCustHistoryData: false,
        customerFieldConfigEnabled: false
    };

    componentDidMount() {
        this.initViewData();
    }

    componentDidUpdate(prevProps, prevState) {
        if (prevProps.cust_mobile != this.props.cust_mobile || prevProps.cust_id != this.props.cust_id) {
            this.setState({
                render_helper: !this.state.render_helper,
            });
            this.initViewData();
        }
    }

    initViewData() {
        // Return early if neither cust_mobile nor cust_id is provided
        if(!this.props.cust_mobile && !this.props.cust_id) return;

        this.setState({
            isLoadingViewData: true,
        });

        const params = {};

        if (this.props.cust_id) {
            params['cust_id'] = this.props.cust_id;
        } else if (this.props.cust_mobile) {
            params['cust_mobile'] = this.props.cust_mobile;
        }

        const onComplete = (resp) => {
            this.setState({
                isLoadingViewData: false,
                viewData: resp.data,
                error: '',
            });
        };
        const onError = (error) => {
            console.log(error.response.status);
            this.setState({
                isLoadingViewData: false,
                error: http_utils.decodeErrorToMessage(error),
            });
        };
        
        http_utils.performGetCall(dataUrl, params, onComplete, onError);
  
    }

    render() {
        const { isLoadingViewData, error, viewData } = this.state;
        const cust_details = this.state.viewData?.cust_detail[0];
        const cust_his_data = this.state.viewData?.cust_his_data;

        return (
            <div className="gx-mb-1">
                {isLoadingViewData ? (
                    <div className="gx-loader-view gx-loader-position">
                        <CircularProgress />
                    </div>
                ) : viewData == undefined ? (
                    <p className="gx-text-red">{error}</p>
                ) : (
                    <div className="gx-module-box-content gx-bg-light-grey">
                        <Card
                            title="Customer History"
                            className=" gx-mb-0 gx-bg-light-grey"
                            extra={
                                <div>
                                    {' '}
                                    <HistoryOutlined />{' '}
                                </div>
                            }
                        >
                            <div className="gx-text-grey gx-mb-1">
                                Registered as {cust_details?.cust_full_name} on{' '}
                                <small>
                                    {convertUTCToDisplayTime(
                                        cust_details?.cust_c_time
                                    )}
                                </small>
                                <br />
                                <p className="gx-mt-1">
                                    {' '}
                                    Service request by customer{' '}
                                </p>
                            </div>
                            <Tabs>
                                {cust_his_data.map(
                                    (singleRecentReq) =>
                                        singleRecentReq.recent_req_details
                                            ?.length > 0 && (
                                            <>
                                                {
                                                    (this.state.isCustHistoryData =
                                                        true && (
                                                            <TabPane
                                                                tab={
                                                                    singleRecentReq.title +
                                                                    ' (' +
                                                                    singleRecentReq
                                                                        .recent_req_details
                                                                        .length +
                                                                    ')'
                                                                }
                                                                key={
                                                                    singleRecentReq.id
                                                                }
                                                            >
                                                                <RecentReqFrCustHistory
                                                                    recent_req={
                                                                        singleRecentReq
                                                                    }
                                                                    openInNewTab
                                                                    isBrand
                                                                    avoidApiCall
                                                                />
                                                            </TabPane>
                                                        ))
                                                }
                                            </>
                                        )
                                )}
                                {!this.state.isCustHistoryData && <NoData />}
                            </Tabs>
                        </Card>
                    </div>
                )}
            </div>
        );
    }
}

export default CustomerHistory;
