CREATE OR REPLACE FUNCTION tms_validate_srvc_hub_details_batch(
    batch_data_ json,
    org_id_ integer,
    vertical_id_ integer
) RETURNS json
LANGUAGE plpgsql
AS $function$
declare
    _single_entry json;
    row_count_ integer := 0;
    hub_name_ text;
    hub_code_ text;
    state_name_ text;
    city_name_ text;
    pin_codes_text_ text;
    hub_pin_codes_ text[];
    city_exists_for_state boolean;
    pin_codes_to_validate text[];
    single_pincode_ text;
    pincode_exists_for_city boolean;
    exception_hint text;

    -- Error collection
    errors_list text[] := '{}';


begin
    -- Validate each entry in batch_data
    for _single_entry in select * from json_array_elements(batch_data_)
    loop
        row_count_ = row_count_ + 1;
        
        -- Extract data from entry
        hub_name_ = _single_entry->>'hub_name';
        hub_code_ = _single_entry->>'hub_code';
        state_name_ = trim(_single_entry->>'state');
        city_name_ = trim(_single_entry->>'city');
        pin_codes_text_ = _single_entry->>'pin_codes';

        -- Validate hub_name length constraint (varchar(100))
        if hub_name_ is not null and length(trim(hub_name_)) > 100 then
            exception_hint = 'Failed row --> ' || hub_name_ || ' (Row ' || row_count_ || '): hub_name_too_long. Hub name exceeds maximum length of 100 characters (current: ' || length(trim(hub_name_)) || ' characters). Please shorten the hub name.';
            errors_list = array_append(errors_list, exception_hint);
        end if;

        -- Validate hub_code length constraint (varchar(20))
        if hub_code_ is not null and length(trim(hub_code_)) > 20 then
            exception_hint = 'Failed row --> ' || hub_name_ || ' (Row ' || row_count_ || '): hub_code_too_long. Hub code "' || hub_code_ || '" exceeds maximum length of 20 characters (current: ' || length(trim(hub_code_)) || ' characters). Please shorten the hub code.';
            errors_list = array_append(errors_list, exception_hint);
        end if;


        -- Validate state is provided
        if state_name_ is null or state_name_ = '' then
            exception_hint = 'Failed row --> ' || hub_name_ || ' (Row ' || row_count_ || '): state_required. State is required for service hub creation.';
            errors_list = array_append(errors_list, exception_hint);
        end if;

        -- Validate city is provided
        if city_name_ is null or city_name_ = '' then
            exception_hint = 'Failed row --> ' || hub_name_ || ' (Row ' || row_count_ || '): city_required. City is required for service hub creation.';
            errors_list = array_append(errors_list, exception_hint);
        end if;

        -- Check if city exists for the given state
        select exists(
            select 1 from sys_cf_loc_mstr
             where lower(trim(state)) = lower(trim(state_name_))
               and lower(trim(dist)) = lower(trim(city_name_))
        ) into city_exists_for_state;

        if not city_exists_for_state then
            exception_hint = 'Failed row --> ' || hub_name_ || ' (Row ' || row_count_ || '): invalid_city. City "' || city_name_ || '" does not exist in state "' || state_name_ || '". Please select a valid city.';
            errors_list = array_append(errors_list, exception_hint);
        end if;

        -- Validate pincodes if provided
        if pin_codes_text_ is not null and trim(pin_codes_text_) != '' then
            -- Split pincodes by comma and trim
            select array_agg(trim(unnest)) into pin_codes_to_validate
              from unnest(string_to_array(pin_codes_text_, ',')) as unnest
             where trim(unnest) != '';

            -- backend validation for pincodes
            foreach single_pincode_ in array pin_codes_to_validate
            loop
                -- Check if pincode contains only numbers
                if not (single_pincode_ ~ '^[0-9]+$') then
                    exception_hint = 'Failed row --> ' || hub_name_ || ' (Row ' || row_count_ || '): invalid_pincode_format. Pincode "' || single_pincode_ || '" must contain only numbers.';
                    errors_list = array_append(errors_list, exception_hint);
                end if;

                -- Check if pincode has exactly 6 digits
                if length(single_pincode_) != 6 then
                    exception_hint = 'Failed row --> ' || hub_name_ || ' (Row ' || row_count_ || '): invalid_pincode_length. Pincode "' || single_pincode_ || '" must be exactly 6 digits long.';
                    errors_list = array_append(errors_list, exception_hint);
                end if;
            end loop;
        end if;

    end loop;

    -- Check if there are any validation errors
    if array_length(errors_list, 1) > 0 then
        return json_build_object(
            'status', false,
            'message', 'Validation failed for ' || array_length(errors_list, 1) || ' entries',
            'hint', array_to_string(errors_list, ' | ')
        );
    end if;

    -- All validations passed, return success
    return json_build_object(
        'status', true,
        'message', 'All ' || row_count_ || ' entries validated successfully',
        'data', null
    );
end;
$function$;
